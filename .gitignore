# weights and biases
.wandb/ 
wandb_api_key.txt

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class
*.DS_Store
*.vscode

# C extensions
*.so
*.py~

# Pycharm
.idea

# vim temp files
*.swp

# Sphinx doc
doc/_build/
doc/source/auto_examples/
doc/source/modules/generated/

# Distribution / packaging
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg
.pytest_cache/

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*,cover
.hypothesis/

# Translations
*.mo
*.pot

# Django stuff:
*.log

# Sphinx documentation
docs/_build/

# PyBuilder
target/

#Ipython Notebook
.ipynb_checkpoints
