{'n_params_baseline': None, 'verbose': True, 'arch': 'fno', 'distributed': {'use_distributed': False, 'wireup_info': 'mpi', 'wireup_store': 'tcp', 'model_parallel_size': 2, 'seed': 666}, 'fno': {'out_channels': 1, 'n_modes': [64, 64], 'hidden_channels': 64, 'projection_channel_ratio': 4, 'n_layers': 4, 'domain_padding': 0.0, 'domain_padding_mode': 'one-sided', 'fft_norm': 'forward', 'norm': None, 'skip': 'linear', 'implementation': 'reconstructed', 'use_channel_mlp': 1, 'channel_mlp_expansion': 0.5, 'channel_mlp_dropout': 0, 'separable': False, 'factorization': None, 'rank': 1.0, 'fixed_rank_modes': None, 'dropout': 0.0, 'tensor_lasso_penalty': 0.0, 'joint_factorization': False, 'stabilizer': None, 'in_channels': 1}, 'opt': {'n_epochs': 300, 'learning_rate': 0.0003, 'training_loss': 'h1', 'weight_decay': 0.0001, 'amp_autocast': False, 'scheduler_T_max': 500, 'scheduler_patience': 50, 'scheduler': 'StepLR', 'step_size': 100, 'gamma': 0.5}, 'data': {'folder': 'data/navier_stokes/', 'batch_size': 8, 'n_train': 3000, 'train_resolution': 128, 'n_tests': [600], 'test_resolutions': [128], 'test_batch_sizes': [8], 'encode_input': True, 'encode_output': True}, 'patching': {'levels': 0, 'padding': 0, 'stitching': False}, 'wandb': {'log': False, 'name': None, 'group': '', 'project': 'train_ns', 'entity': 'dhpitt', 'sweep': False, 'log_output': True, 'eval_interval': 1}}