from typing import List, Optional, Tuple, Union  # 导入类型提示工具

from ..utils import validate_scaling_factor  # 导入分辨率缩放因子验证函数

import torch  # 导入PyTorch库
from torch import nn  # 导入神经网络模块

import tensorly as tl  # 导入tensorly库，用于张量操作
from tensorly.plugins import use_opt_einsum  # 导入优化版本的爱因斯坦求和
from tltorch.factorized_tensors.core import FactorizedTensor  # 导入因子化张量类

from .einsum_utils import einsum_complexhalf  # 导入复数半精度爱因斯坦求和
from .base_spectral_conv import BaseSpectralConv  # 导入频谱卷积基类
from .resample import resample  # 导入重采样函数

tl.set_backend("pytorch")  # 设置tensorly后端为PyTorch
use_opt_einsum("optimal")  # 使用最优爱因斯坦求和方法
einsum_symbols = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"  # 爱因斯坦求和使用的符号


def _contract_dense(x, weight, separable=False):
    """
    执行密集张量权重与输入的收缩操作

    参数:
    x: 输入张量
    weight: 权重张量
    separable: 是否使用可分离卷积
    """
    order = tl.ndim(x)  # 获取输入张量的维度
    # 批次大小, 输入通道, x, y...
    x_syms = list(einsum_symbols[:order])  # 为输入张量各维度分配符号

    # 输入通道, 输出通道, x, y...
    weight_syms = list(x_syms[1:])  # 为权重张量分配符号(不包括批次维度)

    # 批次大小, 输出通道, x, y...
    if separable:  # 如果是可分离卷积
        out_syms = [x_syms[0]] + list(weight_syms)  # 输出符号与输入保持相同
    else:  # 普通卷积
        weight_syms.insert(1, einsum_symbols[order])  # 在权重符号中插入输出通道符号
        out_syms = list(weight_syms)  # 输出符号基于权重符号
        out_syms[0] = x_syms[0]  # 保持批次维度符号不变

    # 构建爱因斯坦求和方程
    eq = f'{"".join(x_syms)},{"".join(weight_syms)}->{"".join(out_syms)}'

    # 如果权重不是张量，将其转换为张量
    if not torch.is_tensor(weight):
        weight = weight.to_tensor()

    # 根据精度选择适当的爱因斯坦求和函数
    if x.dtype == torch.complex32:
        # 如果输入是半精度复数，使用专用的爱因斯坦求和函数
        return einsum_complexhalf(eq, x, weight)
    else:
        # 否则使用标准爱因斯坦求和
        return tl.einsum(eq, x, weight)


def _contract_dense_separable(x, weight, separable):
    """
    执行可分离密集张量收缩(元素级乘法)

    参数:
    x: 输入张量
    weight: 权重张量
    separable: 未使用参数(为保持接口一致)
    """
    if not torch.is_tensor(weight):
        weight = weight.to_tensor()  # 将权重转换为张量
    return x * weight  # 元素级乘法


def _contract_cp(x, cp_weight, separable=False):
    """
    使用CP(CANDECOMP/PARAFAC)分解权重与输入执行收缩

    参数:
    x: 输入张量
    cp_weight: CP分解权重
    separable: 是否使用可分离卷积
    """
    order = tl.ndim(x)  # 获取输入张量的维度

    x_syms = str(einsum_symbols[:order])  # 输入张量符号
    rank_sym = einsum_symbols[order]  # 秩符号
    out_sym = einsum_symbols[order + 1]  # 输出通道符号
    out_syms = list(x_syms)  # 输出符号基于输入符号
    if separable:  # 可分离卷积
        factor_syms = [einsum_symbols[1] + rank_sym]  # 仅输入通道
    else:  # 普通卷积
        out_syms[1] = out_sym  # 替换输出符号中的输入通道为输出通道
        factor_syms = [einsum_symbols[1] + rank_sym, out_sym + rank_sym]  # 输入通道和输出通道
    factor_syms += [xs + rank_sym for xs in x_syms[2:]]  # 添加空间维度符号 x, y, ...
    # 构建爱因斯坦求和方程
    eq = f'{x_syms},{rank_sym},{",".join(factor_syms)}->{"".join(out_syms)}'

    # 根据精度选择适当的爱因斯坦求和函数
    if x.dtype == torch.complex32:
        return einsum_complexhalf(eq, x, cp_weight.weights, *cp_weight.factors)
    else:
        return tl.einsum(eq, x, cp_weight.weights, *cp_weight.factors)


def _contract_tucker(x, tucker_weight, separable=False):
    """
    使用Tucker分解权重与输入执行收缩

    参数:
    x: 输入张量
    tucker_weight: Tucker分解权重
    separable: 是否使用可分离卷积
    """
    order = tl.ndim(x)  # 获取输入张量的维度

    x_syms = str(einsum_symbols[:order])  # 输入张量符号
    out_sym = einsum_symbols[order]  # 输出通道符号
    out_syms = list(x_syms)  # 输出符号基于输入符号
    if separable:  # 可分离卷积
        core_syms = einsum_symbols[order + 1: 2 * order]  # 核心张量符号
        # 各维度的因子符号
        factor_syms = [xs + rs for (xs, rs) in zip(x_syms[1:], core_syms)]

    else:  # 普通卷积
        core_syms = einsum_symbols[order + 1: 2 * order + 1]  # 核心张量符号
        out_syms[1] = out_sym  # 替换输出符号中的输入通道为输出通道
        factor_syms = [
            einsum_symbols[1] + core_syms[0],
            out_sym + core_syms[1],
        ]  # 输入通道和输出通道
        # 添加空间维度的因子符号 x, y, ...
        factor_syms += [xs + rs for (xs, rs) in zip(x_syms[2:], core_syms[2:])]

    # 构建爱因斯坦求和方程
    eq = f'{x_syms},{core_syms},{",".join(factor_syms)}->{"".join(out_syms)}'

    # 根据精度选择适当的爱因斯坦求和函数
    if x.dtype == torch.complex32:
        return einsum_complexhalf(eq, x, tucker_weight.core, *tucker_weight.factors)
    else:
        return tl.einsum(eq, x, tucker_weight.core, *tucker_weight.factors)


def _contract_tt(x, tt_weight, separable=False):
    """
    使用TT(张量列车)分解权重与输入执行收缩

    参数:
    x: 输入张量
    tt_weight: TT分解权重
    separable: 是否使用可分离卷积
    """
    order = tl.ndim(x)  # 获取输入张量的维度

    x_syms = list(einsum_symbols[:order])  # 输入张量符号
    weight_syms = list(x_syms[1:])  # 权重张量符号(不包括批次维度)
    if not separable:  # 普通卷积
        weight_syms.insert(1, einsum_symbols[order])  # 插入输出通道符号
        out_syms = list(weight_syms)  # 输出符号基于权重符号
        out_syms[0] = x_syms[0]  # 保持批次维度符号不变
    else:  # 可分离卷积
        out_syms = list(x_syms)  # 输出符号与输入相同
    rank_syms = list(einsum_symbols[order + 1:])  # 秩符号
    tt_syms = []  # TT核心符号
    for i, s in enumerate(weight_syms):
        tt_syms.append([rank_syms[i], s, rank_syms[i + 1]])  # 为每个TT核心分配符号
    # 构建爱因斯坦求和方程
    eq = (
            "".join(x_syms)
            + ","
            + ",".join("".join(f) for f in tt_syms)
            + "->"
            + "".join(out_syms)
    )

    # 根据精度选择适当的爱因斯坦求和函数
    if x.dtype == torch.complex32:
        return einsum_complexhalf(eq, x, *tt_weight.factors)
    else:
        return tl.einsum(eq, x, *tt_weight.factors)


def get_contract_fun(weight, implementation="reconstructed", separable=False):
    """
    获取适合特定权重类型的收缩函数

    参数:
    weight: 权重张量或因子化张量
    implementation: {'reconstructed', 'factorized'}, 默认为'reconstructed'
        - 'reconstructed': 重建权重张量再进行前向传播
        - 'factorized': 直接使用因子化权重与输入收缩
    separable: 是否使用可分离卷积

    返回:
    function: 执行(x, weight) -> x * weight的函数(在傅里叶空间)
    """
    if implementation == "reconstructed":  # 重建模式
        if separable:
            return _contract_dense_separable  # 可分离密集收缩
        else:
            return _contract_dense  # 密集收缩
    elif implementation == "factorized":  # 因子化模式
        if torch.is_tensor(weight):  # 如果是普通张量
            return _contract_dense
        elif isinstance(weight, FactorizedTensor):  # 如果是因子化张量
            if weight.name.lower().endswith("dense"):
                return _contract_dense
            elif weight.name.lower().endswith("tucker"):
                return _contract_tucker  # Tucker分解收缩
            elif weight.name.lower().endswith("tt"):
                return _contract_tt  # TT分解收缩
            elif weight.name.lower().endswith("cp"):
                return _contract_cp  # CP分解收缩
            else:
                raise ValueError(f"遇到意外的因子化权重类型 {weight.name}")
        else:
            raise ValueError(
                f"遇到意外的权重类型 {weight.__class__.__name__}"
            )
    else:
        raise ValueError(
            f'implementation={implementation}，但期望的值是"reconstructed"或"factorized"'
        )


Number = Union[int, float]  # 定义Number类型为整数或浮点数


class SpectralConv(BaseSpectralConv):
    """
    SpectralConv实现了[1]和[2]中描述的傅里叶层的频谱卷积组件。

    参数:
    ----------
    in_channels: int
        输入通道数
    out_channels: int
        输出通道数
    n_modes: int或int元组
        训练期间在傅里叶域中用于收缩的模式数量。

        警告:

            我们处理了傅里叶模式中的冗余性，因此，对于大小为I_1, ..., I_N的输入，
            请提供满足I_1 < M_K <= I_N的模式M_K。
            我们将自动保留正确数量的模式：具体来说，仅对于最后一个模式，
            如果您指定M_N个模式，我们将使用M_N // 2 + 1个模式，
            因为实数FFT在最后一维上是冗余的。有关模式截断的更多信息，
            请参考:ref:`fourier_layer_impl`


        注意:

            提供的模式应为偶数。奇数将四舍五入到最接近的偶数。

        这可以在训练期间动态更新。

    max_n_modes: int元组或None，默认为None
        * 如果不是None，表示沿每个维度在傅里叶层中保留的模式的**最大**数量
            模式数量(`n_modes`)不能增加超过该值。
        * 如果为None，则使用所有n_modes。

    separable: bool，默认为True
        是否使用可分离收缩实现
        如果为True，单独收缩因子化张量权重的因子
    init_std: float或'auto'，默认为'auto'
        初始化使用的标准差
    factorization: str或None，{'tucker', 'cp', 'tt'}，默认为None
        如果为None，则为FNO学习单个密集权重。
        否则，该权重(用于傅里叶域中的收缩)以因子化形式学习。
        在这种情况下，`factorization`是所用参数权重的张量分解。
    rank: float或rank，可选
        傅里叶权重张量分解的秩，默认为1.0
        如果``factorization为None``则忽略
    fixed_rank_modes: bool，可选
        不进行因子分解的模式，默认为False
        如果``factorization为None``则忽略
    fft_norm: str，可选
        fft归一化参数，默认为'forward'
    implementation: {'factorized', 'reconstructed'}，可选，默认为'factorized'
        如果factorization不为None，要使用的前向模式:
        * `reconstructed`: 从分解重建完整权重张量并用于前向传播
        * `factorized`: 输入直接与分解的因子收缩
        如果``factorization为None``则忽略
    decomposition_kwargs: dict，可选，默认为{}
        可选的传递给张量分解的附加参数
        如果``factorization为None``则忽略
    complex_data: bool，可选
        数据在空间域中是否为复值，默认为False
        如果为True，则使用不同的FFT收缩逻辑，并使用完整FFT而不是实值FFT

    参考文献:
    -----------
    .. [1]:

    Li, Z. et al. "Fourier Neural Operator for Parametric Partial Differential
        Equations" (2021). ICLR 2021, https://arxiv.org/pdf/2010.08895.

    .. [2]:

    Kossaifi, J., Kovachki, N., Azizzadenesheli, K., Anandkumar, A. "Multi-Grid
        Tensorized Fourier Neural Operator for High-Resolution PDEs" (2024).
        TMLR 2024, https://openreview.net/pdf?id=AWiDlO63bH.
    """

    def __init__(
            self,
            in_channels,  # 输入通道数
            out_channels,  # 输出通道数
            n_modes,  # 各维度的傅里叶模式数量
            complex_data=False,  # 数据是否在空间域为复值
            max_n_modes=None,  # 最大模式数量
            bias=True,  # 是否使用偏置
            separable=False,  # 是否使用可分离卷积
            resolution_scaling_factor: Optional[Union[Number, List[Number]]] = None,  # 分辨率缩放因子
            fno_block_precision="full",  # FNO块精度模式
            rank=0.5,  # 张量分解的秩
            factorization=None,  # 张量分解方法
            implementation="reconstructed",  # 实现方式
            fixed_rank_modes=False,  # 固定秩模式
            decomposition_kwargs: Optional[dict] = None,  # 分解额外参数
            init_std="auto",  # 初始化标准差
            fft_norm="forward",  # FFT归一化方式
            device=None,  # 设备
    ):
        super().__init__(device=device)  # 调用基类初始化

        self.in_channels = in_channels  # 设置输入通道数
        self.out_channels = out_channels  # 设置输出通道数

        self.complex_data = complex_data  # 设置是否使用复数据

        # n_modes是每个维度上保留的模式总数
        self.n_modes = n_modes  # 设置模式数量
        self.order = len(self.n_modes)  # 确定FNO维度(1D, 2D, 3D等)

        # 设置最大模式数量
        if max_n_modes is None:
            max_n_modes = self.n_modes
        elif isinstance(max_n_modes, int):
            max_n_modes = [max_n_modes]
        self.max_n_modes = max_n_modes

        self.fno_block_precision = fno_block_precision  # 设置FNO块精度
        self.rank = rank  # 设置秩
        self.factorization = factorization  # 设置分解方法
        self.implementation = implementation  # 设置实现方式

        # 验证并设置分辨率缩放因子
        self.resolution_scaling_factor: Union[
            None, List[List[float]]
        ] = validate_scaling_factor(resolution_scaling_factor, self.order)

        # 设置初始化标准差
        if init_std == "auto":
            init_std = (2 / (in_channels + out_channels)) ** 0.5  # Xavier初始化
        else:
            init_std = init_std

        # 处理固定秩模式
        if isinstance(fixed_rank_modes, bool):
            if fixed_rank_modes:
                # 如果是布尔值，保持层数固定
                fixed_rank_modes = [0]
            else:
                fixed_rank_modes = None
        self.fft_norm = fft_norm  # 设置FFT归一化

        # 设置分解方法，如果为None则使用Dense(无分解)
        if factorization is None:
            factorization = "Dense"  # 无分解

        # 设置权重形状，考虑是否使用可分离卷积
        if separable:
            if in_channels != out_channels:
                raise ValueError(
                    "要使用可分离傅里叶卷积，in_channels必须等于"
                    f"out_channels，但得到in_channels={in_channels}和"
                    f"out_channels={out_channels}",
                )
            weight_shape = (in_channels, *max_n_modes)  # 可分离卷积权重形状
        else:
            weight_shape = (in_channels, out_channels, *max_n_modes)  # 普通卷积权重形状
        self.separable = separable  # 保存可分离标志

        # 准备分解参数
        tensor_kwargs = decomposition_kwargs if decomposition_kwargs is not None else {}

        # 创建/初始化频谱权重张量
        if factorization is None:
            self.weight = torch.tensor(weight_shape, dtype=torch.cfloat)  # 创建复数张量
        else:
            # 创建因子化张量
            self.weight = FactorizedTensor.new(weight_shape, rank=self.rank,
                                               factorization=factorization, fixed_rank_modes=fixed_rank_modes,
                                               **tensor_kwargs, dtype=torch.cfloat)
        self.weight.normal_(0, init_std)  # 使用正态分布初始化权重

        # 获取适合当前配置的收缩函数
        self._contract = get_contract_fun(
            self.weight, implementation=implementation, separable=separable
        )

        # 创建偏置参数(如果需要)
        if bias:
            self.bias = nn.Parameter(
                init_std * torch.randn(*(tuple([self.out_channels]) + (1,) * self.order))
            )
        else:
            self.bias = None

    def transform(self, x, output_shape=None):
        """
        处理输入形状变换，支持可变分辨率

        参数:
        x: 输入张量
        output_shape: 输出形状(可选)
        """
        in_shape = list(x.shape[2:])  # 获取输入空间维度形状

        # 根据分辨率缩放因子或指定输出形状确定输出形状
        if self.resolution_scaling_factor is not None and output_shape is None:
            out_shape = tuple(
                [round(s * r) for (s, r) in zip(in_shape, self.resolution_scaling_factor)]
            )
        elif output_shape is not None:
            out_shape = output_shape
        else:
            out_shape = in_shape

        # 如果输入形状等于输出形状，无需变换
        if in_shape == out_shape:
            return x
        else:
            # 否则重采样到目标形状
            return resample(x, 1.0, list(range(2, x.ndim)), output_shape=out_shape)

    @property
    def n_modes(self):
        """获取模式数量"""
        return self._n_modes

    @n_modes.setter
    def n_modes(self, n_modes):
        """设置模式数量，处理FFT冗余性"""
        if isinstance(n_modes, int):  # 应该只发生在1D FNO中
            n_modes = [n_modes]
        else:
            n_modes = list(n_modes)
        # 实数FFT是反对称的，所以如果数据在空间域是实数，最后一个模式有冗余
        # 作为设计选择，我们在这里进行操作，避免用户处理+1
        # 如果我们使用完整FFT，我们不能从最后一个模式截断信息
        if not self.complex_data:
            n_modes[-1] = n_modes[-1] // 2 + 1
        self._n_modes = n_modes

    def forward(
            self, x: torch.Tensor, output_shape: Optional[Tuple[int]] = None
    ):
        """
        因子化频谱卷积的通用前向传播

        参数:
        x: torch.Tensor
            大小为(batch_size, channels, d1, ..., dN)的输入激活
        output_shape: 可选的输出形状

        返回:
        频谱卷积结果
        """
        batchsize, channels, *mode_sizes = x.shape  # 解析输入形状

        # 计算FFT大小，考虑FFT对称性
        fft_size = list(mode_sizes)
        if not self.complex_data:
            fft_size[-1] = fft_size[-1] // 2 + 1  # 实空间数据的最后一个系数冗余
        fft_dims = list(range(-self.order, 0))  # FFT变换的维度

        # 根据精度模式设置数据类型
        if self.fno_block_precision == "half":
            x = x.half()  # 转换为半精度

        # 执行FFT变换，将数据从空间域转换到频域
        if self.complex_data:
            # 对复数据使用完整FFT
            x = torch.fft.fftn(x, norm=self.fft_norm, dim=fft_dims)
            dims_to_fft_shift = fft_dims
        else:
            # 对实数据使用实数FFT
            x = torch.fft.rfftn(x, norm=self.fft_norm, dim=fft_dims)
            # 当x在空间域是实数时，最后一维的后半部分是冗余的。
            # 关于FFT移位的讨论，请参见:ref:`fft_shift_explanation`。
            dims_to_fft_shift = fft_dims[:-1]

            # 对多维数据执行FFT移位，将零频率移至中心
        if self.order > 1:
            x = torch.fft.fftshift(x, dim=dims_to_fft_shift)

        # 混合精度模式：上面的FFT在全精度下运行，但以下操作在半精度下运行
        if self.fno_block_precision == "mixed":
            x = x.chalf()  # 转换为复数半精度

        # 设置输出FFT的数据类型
        if self.fno_block_precision in ["half", "mixed"]:
            out_dtype = torch.chalf
        else:
            out_dtype = torch.cfloat

        # 创建输出FFT张量，初始化为零
        out_fft = torch.zeros([batchsize, self.out_channels, *fft_size],
                              device=x.device, dtype=out_dtype)

        # 如果当前模式少于最大模式，则开始从权重张量的中心附近索引模式
        starts = [(max_modes - min(size, n_mode)) for (size, n_mode, max_modes) in
                  zip(fft_size, self.n_modes, self.max_n_modes)]
        # 如果收缩是可分离的，权重形状为(channels, modes_x, ...)
        # 否则形状为(in_channels, out_channels, modes_x, ...)
        if self.separable:
            slices_w = [slice(None)]  # 通道
        else:
            slices_w = [slice(None), slice(None)]  # 输入通道, 输出通道

        # 构建权重张量的切片
        if self.complex_data:
            slices_w += [slice(start // 2, -start // 2) if start else slice(start, None) for start in starts]
        else:
            # 实FFT中最后一个模式已经移除了冗余的一半
            slices_w += [slice(start // 2, -start // 2) if start else slice(start, None) for start in starts[:-1]]
            slices_w += [slice(None, -starts[-1]) if starts[-1] else slice(None)]

        # 取出需要的权重部分
        weight = self.weight[slices_w]

        ### 沿每个维度选择FFT信号的前n_modes个模式

        # 如果是可分离卷积，权重张量只有一个通道维度
        if self.separable:
            weight_start_idx = 1
        # 否则删除前两个维度(输入通道，输出通道)
        else:
            weight_start_idx = 2

        # 为输入张量构建切片
        slices_x = [slice(None), slice(None)]  # 批次大小, 通道

        # 为每个维度构建切片，选择中心附近的低频部分
        for all_modes, kept_modes in zip(fft_size, list(weight.shape[weight_start_idx:])):
            # FFT移位后，第0频率位于每个方向的n//2位置
            # 我们通过获取以下索引，在第0频率(位于索引n//2)周围选择n_modes个模式
            # n//2 - n_modes//2  到  n//2 + n_modes//2       如果n_modes是偶数
            # n//2 - n_modes//2  到  n//2 + n_modes//2 + 1   如果n_modes是奇数
            center = all_modes // 2  # 中心位置(第0频率)
            negative_freqs = kept_modes // 2  # 负频率数量
            positive_freqs = kept_modes // 2 + kept_modes % 2  # 正频率数量

            # 这个切片表示沿每个维度的所需索引
            slices_x += [slice(center - negative_freqs, center + positive_freqs)]

        # 处理最后一个维度的特殊情况
        if weight.shape[-1] < fft_size[-1]:
            slices_x[-1] = slice(None, weight.shape[-1])
        else:
            slices_x[-1] = slice(None)

        # 执行频域卷积(实际上是逐点复数乘法)
        out_fft[slices_x] = self._contract(x[slices_x], weight, separable=self.separable)

        # 根据分辨率缩放因子或输出形状调整输出的空间维度大小
        if self.resolution_scaling_factor is not None and output_shape is None:
            mode_sizes = tuple([round(s * r) for (s, r) in zip(mode_sizes, self.resolution_scaling_factor)])

        if output_shape is not None:
            mode_sizes = output_shape

        # 对多维数据进行逆FFT移位
        if self.order > 1:
            out_fft = torch.fft.fftshift(out_fft, dim=fft_dims[:-1])

        # 执行逆FFT变换，将数据从频域转回空间域
        if self.complex_data:
            x = torch.fft.ifftn(out_fft, s=mode_sizes, dim=fft_dims, norm=self.fft_norm)
        else:
            x = torch.fft.irfftn(out_fft, s=mode_sizes, dim=fft_dims, norm=self.fft_norm)

        # 如果有偏置，添加到结果
        if self.bias is not None:
            x = x + self.bias

        return x  # 返回频谱卷积结果