# 傅里叶神经算子（FNO）物理场预测

本项目使用傅里叶神经算子（Fourier Neural Operator, FNO）模型进行相场、温度场和溶质场的时序预测。

## 环境要求

- Python 3.8+
- PyTorch 1.8+
- NumPy
- Matplotlib
- scikit-image (可选，用于SSIM计算)
- tqdm

## 项目结构

```
.
├── PhaseField_Data/       # 相场CSV数据
├── PhaseField_Images/     # 相场PNG图像
├── TemperatureField_Data/ # 温度场CSV数据
├── TemperatureField_Images/ # 温度场PNG图像
├── ConcentrationField_Data/ # 溶质场CSV数据
├── ConcentrationField_Images/ # 溶质场PNG图像
├── fno_models/            # 保存训练好的模型
│   ├── phi/               # 相场模型
│   ├── temp/              # 温度场模型
│   └── concentration/     # 溶质场模型
├── predictions/           # 预测结果
├── train_phase_field.py   # 相场训练脚本
├── train_temperature_field.py # 温度场训练脚本
├── train_concentration_field.py # 溶质场训练脚本
├── run_phase_prediction.py     # 相场预测脚本
├── run_temperature_prediction.py  # 温度场预测脚本
└── run_concentration_prediction.py # 溶质场预测脚本
```

## 训练模型

### 相场模型训练

```bash
python train_phase_field.py --data_dir ./PhaseField_Data --num_epochs 10 --modes 12 --width 32 --sample_stride 5 --save_dir ./fno_models/phi
```

主要参数:
- `--data_dir`: 数据目录路径
- `--num_epochs`: 训练轮数
- `--modes`: 傅里叶模式数量
- `--width`: 网络宽度
- `--sample_stride`: 帧间隔步长
- `--downsample_factor`: 降采样因子，默认为4(800×800降采样至200×200)
- `--save_dir`: 模型保存目录，默认为./fno_models/phi

### 温度场模型训练

```bash
python train_temperature_field.py --data_dir ./TemperatureField_Data --num_epochs 10 --modes 12 --width 32 --sample_stride 10 --save_dir ./fno_models/temp
```

参数与相场模型类似。

### 溶质场模型训练

```bash
python train_concentration_field.py --data_dir ./ConcentrationField_Data --num_epochs 10 --modes 16 --width 48 --sample_stride 5 --save_dir ./fno_models/concentration
```

溶质场模型默认使用更大的网络宽度和更多的傅里叶模式。

## 模型预测

### 相场预测

```bash
python run_phase_prediction.py --model_path ./fno_models/phi/phase_field_model.pth --input_img ./PhaseField_Images/相场第2950步.png --target_img ./PhaseField_Images/相场第3000步.png
```

主要参数:
- `--model_path`: 模型路径
- `--input_img`: 输入图像路径
- `--target_img`: 目标图像路径
- `--output_dir`: 输出目录
- `--modes`: 傅里叶模式数量
- `--width`: 网络宽度

### 温度场预测

```bash
python run_temperature_prediction.py --model_path ./fno_models/temp/temp_model.pth --input_img ./TemperatureField_Images/温度场第2950步.png --target_img ./TemperatureField_Images/温度场第3000步.png
```

### 溶质场预测

```bash
python run_concentration_prediction.py --model_path ./fno_models/concentration/solute_model.pth --input_img ./ConcentrationField_Images/溶质场第2950步.png --target_img ./ConcentrationField_Images/溶质场第3000步.png
```

## 可视化结果

预测脚本将生成包含以下内容的可视化结果:
1. 输入场
2. 预测场
3. 真实场
4. 误差分布

同时会计算并输出:
- MSE (均方误差)
- MAE (平均绝对误差)
- 最大误差
- 平均误差
- 推理时间

## 模型架构

本项目使用2D傅里叶神经算子(FNO2d)模型，这是一种基于傅里叶变换的神经网络，特别适合物理场问题。主要特点:

1. 使用快速傅里叶变换(FFT)在频域学习物理场的动态演化
2. 保留低频傅里叶模式，减少计算复杂度
3. 结合频域和空间域的特征提取
4. 使用残差连接改善梯度流动

## 注意事项

1. 训练前请确保有足够的GPU内存，降低批量大小或降采样因子可以减少内存占用
2. 对于不同物理场，可能需要调整网络参数(模式数、网络宽度等)
3. CSV数据格式应为普通的逗号分隔数值矩阵
4. PNG图像应为灰度图 