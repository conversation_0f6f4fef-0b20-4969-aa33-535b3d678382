# FNO可视化工具使用说明

基于PyTorch FNO模型的三个物理场可视化分析工具，支持Ground Truth vs Prediction对比分析。

## 工具概览

| 工具文件 | 物理场 | 功能 | 主要用途 |
|----------|--------|------|----------|
| `fno_phase_multi_line_comparison.py` | 相场 | 多行/列对比 | 在同一图中显示多条线的GT vs Pred对比 |
| `fno_phase_temporal_comparison.py` | 相场 | 时间步对比 | 显示同一位置不同时间步的预测演化 |
| `fno_temperature_multi_line_comparison.py` | 温度场 | 多行/列对比 | 温度场的多线对比分析 |
| `fno_temperature_temporal_comparison.py` | 温度场 | 时间步对比 | 温度场的时间演化预测对比 |
| `fno_concentration_multi_line_comparison.py` | 溶质场 | 多行/列对比 | 溶质浓度场的多线对比分析 |
| `fno_concentration_temporal_comparison.py` | 溶质场 | 时间步对比 | 溶质场的时间演化预测对比 |

## 核心特性

### 1. **自动模型检测**
- 自动查找训练好的FNO模型文件
- 支持多个默认路径搜索
- 智能参数匹配（modes, width等）

### 2. **交互式控制台**
- 友好的中文交互界面
- 参数验证和错误提示
- 智能默认值设置

### 3. **灵活的数据处理**
- 支持不同的归一化方式（相场/溶质场：Min-Max，温度场：固定范围）
- 可配置的降采样因子
- 自动数据范围检测

### 4. **专业的可视化**
- 美观的图表设计
- 详细的统计信息显示
- 高分辨率图片保存
- 智能图例排列

## 使用方法

### 交互式模式（推荐）

```bash
# 相场多行对比
python fno_phase_multi_line_comparison.py -i

# 相场时间步对比
python fno_phase_temporal_comparison.py -i

# 温度场多行对比
python fno_temperature_multi_line_comparison.py -i

# 温度场时间步对比
python fno_temperature_temporal_comparison.py -i

# 溶质场多行对比
python fno_concentration_multi_line_comparison.py -i

# 溶质场时间步对比
python fno_concentration_temporal_comparison.py -i
```

### 命令行模式

```bash
# 相场多行对比示例
python fno_phase_multi_line_comparison.py \
    --data_dir ./PhaseField_Data/ \
    --model_path ./fno_models/phi/phase_field_model.pth \
    --time_step 2000 \
    --line_type row \
    --line_indices "300,400,500" \
    --downsample_factor 4

# 温度场时间步对比示例
python fno_temperature_temporal_comparison.py \
    --data_dir ./TemperatureField_Data/ \
    --model_path ./fno_models/temp/temperature_field_model.pth \
    --line_type row \
    --line_index 400 \
    --time_steps "500,1000,1500,2000" \
    --downsample_factor 4
```

## 参数说明

### 通用参数

| 参数 | 说明 | 默认值 | 示例 |
|------|------|--------|------|
| `--data_dir` | 数据目录路径 | 各场不同 | `./PhaseField_Data/` |
| `--model_path` | FNO模型路径 | 自动检测 | `./fno_models/phi/phase_field_model.pth` |
| `--downsample_factor` | 降采样因子 | 4 | 4 |
| `--modes` | FNO模型modes参数 | 相场:12, 温度:14, 溶质:16 | 12 |
| `--width` | FNO模型width参数 | 相场/温度:32, 溶质:48 | 32 |
| `--save_path` | 图片保存路径 | 自动生成 | `./results/phase_multi_row_gt_vs_pred_2000_20241221_143022.png` |

### 多行对比工具特有参数

| 参数 | 说明 | 默认值 | 示例 |
|------|------|--------|------|
| `--time_step` | 要分析的时间步 | 2000 | 2000 |
| `--line_type` | 对比类型 | row | row, col |
| `--line_indices` | 行/列索引列表 | "100,400,700" | "300,400,500" 或 "300-500" |

### 时间步对比工具特有参数

| 参数 | 说明 | 默认值 | 示例 |
|------|------|--------|------|
| `--line_type` | 对比类型 | row | row, col |
| `--line_index` | 要分析的行/列索引 | 400 | 400 |
| `--time_steps` | 时间步列表 | "500,1000,2000,3000" | "500,1000,1500,2000" 或 "500-2000-500" |

## 数据格式要求

### 文件命名规范
- **相场数据**: `相场第{step}步.csv`
- **温度场数据**: `温度场第{step}步.csv`
- **溶质场数据**: `溶质场第{step}步.csv`

### 数据格式
- CSV格式，无表头
- 数值类型：float32
- 编码：UTF-8

## 模型文件要求

### 文件格式
- PyTorch模型文件（.pth格式）
- 包含完整的state_dict

### 默认搜索路径
- **相场模型**: `./fno_models/phi/phase_field_model.pth`
- **温度场模型**: `./fno_models/temp/temperature_field_model.pth`
- **溶质场模型**: `./fno_models/con/concentration_field_model.pth`

### 模型参数
| 物理场 | modes | width | dropout | 特殊处理 |
|--------|-------|-------|---------|----------|
| 相场 | 12 | 32 | 0.1 | 边界感知损失 |
| 温度场 | 14 | 32 | 0.1 | 固定范围归一化(234-253K) |
| 溶质场 | 16 | 48 | 0.1 | 高dropout防过拟合 |

## 输出结果

### 图片文件
- 高分辨率PNG格式（300 DPI）
- 自动时间戳命名
- 保存在`./results/`目录下

### 控制台输出
- 详细的处理进度信息
- 每条线/每个时间步的统计信息
- 总体误差分析（MAE）
- 数据范围统计

### 统计信息
- **MAE**: 平均绝对误差
- **数据范围**: GT和Prediction的最小值/最大值
- **处理状态**: 成功/跳过的数据统计

## 故障排除

### 常见问题

1. **模型文件未找到**
   ```
   ❌ 未找到可用的相场模型文件
   ```
   **解决方案**: 确保模型文件存在于默认路径，或使用`--model_path`指定正确路径

2. **数据文件不存在**
   ```
   ❌ 文件不存在: ./PhaseField_Data/相场第2000步.csv
   ```
   **解决方案**: 检查数据目录路径和文件命名格式

3. **索引超出范围**
   ```
   ⚠️  跳过 row 800: 行索引 800 超出数据范围 [0, 799]
   ```
   **解决方案**: 使用有效的行/列索引范围

4. **CUDA内存不足**
   ```
   RuntimeError: CUDA out of memory
   ```
   **解决方案**: 增加降采样因子或使用CPU模式

### 性能优化建议

1. **使用GPU加速**: 确保CUDA可用以提高预测速度
2. **合理设置降采样因子**: 平衡精度和性能
3. **批量处理**: 一次处理多个时间步或多条线
4. **内存管理**: 定期清理CUDA缓存

## 扩展功能

这些工具可以轻松扩展以支持：
- 更多物理场类型
- 不同的模型架构
- 自定义损失函数分析
- 批量处理脚本
- 动画生成功能

## 技术支持

如有问题或建议，请检查：
1. 数据格式是否正确
2. 模型文件是否完整
3. 参数设置是否合理
4. 依赖库是否正确安装
