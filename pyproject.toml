[project]
name = "neuraloperator"
description = "NeuralOperator: Learning in Infinite Dimensions"
authors = [
    {name = "<PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>, <PERSON>, <PERSON><PERSON><PERSON>", email="<EMAIL>"},
]
dependencies = [
    "wandb",
    "ruamel-yaml",
    "configmypy",
    "tensorly",
    "tensorly-torch",
    "matplotlib",
    "numpy>=1.25",
    "opt-einsum",
    "h5py",
]
requires-python = ">=3.9"
readme = "README.rst"
license = {text = "MIT"}

# Dynamic versioning
dynamic = ["version"]

# Required config for build system
[build-system]
requires = ["setuptools>=64"]
build-backend = "setuptools.build_meta"

[tool.setuptools.packages.find]
include = [
    "neuralop",
    "neuralop.*",    
]

# Dynamic versioning from neuralop.__init__
[tool.setuptools.dynamic]
version = {attr = "neuralop.__version__"}

[project.optional-dependencies]
dev = [
    "pytest",
    "flaky",
    "black"

]
doc = [
    "matplotlib",
    "myst-nb",
    "numpydoc",
    "numpy>=1.25",
    "sphinx",
    "sphinx-gallery",
    "tensorly_sphinx_theme",
    "torchtnt",
    "torch_harmonics==0.7.4"
]
all = [
    "pytest",
    "flaky",
    "black",
    "matplotlib",
    "myst-nb",
    "numpydoc",
    "sphinx",
    "sphinx-gallery",
    "tensorly_sphinx_theme",
    "torchtnt",
    "numpy>=1.25",
    "scipy",
    "torch_harmonics==0.7.4"
]
