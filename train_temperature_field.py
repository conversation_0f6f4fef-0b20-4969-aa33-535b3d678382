import os
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import matplotlib.pyplot as plt
from pathlib import Path
import time
import glob
import json
from tqdm import tqdm
import argparse
from PIL import Image  # 确保导入PIL以加载图像

# 检查CUDA是否可用
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"使用设备: {device}")

# 设置随机种子以确保可重复性
torch.manual_seed(42)
np.random.seed(42)

# 定义数据集类
class TemperatureFieldDataset(Dataset):
    def __init__(self, data_dir, start_idx=0, end_idx=3000, transform=None, use_cache=True, stride=10, downsample_factor=4):
        """
        加载温度场数据集
        
        参数:
            data_dir (str): 数据目录路径
            start_idx (int): 起始帧索引
            end_idx (int): 结束帧索引
            transform (callable, optional): 可选的转换函数
            use_cache (bool): 是否使用缓存加速
            stride (int): 采样步长，控制相邻帧对的间隔
            downsample_factor (int): 数据降采样因子，控制空间分辨率
        """
        self.data_dir = data_dir
        self.transform = transform
        self.file_pairs = []
        self.cache = {}
        self.use_cache = use_cache
        self.stride = stride
        self.downsample_factor = downsample_factor
        self.start_idx = start_idx
        self.end_idx = end_idx
        
        # 温度场的全局归一化参数 - 使用固定范围而不是每个文件单独归一化
        self.temp_min = 234.0  # 温度场的最小值
        self.temp_max = 253.0  # 温度场的最大值
        
        # 获取指定范围内的所有文件
        all_files = sorted(glob.glob(os.path.join(data_dir, "温度场第*步.csv")))
        
        # 筛选出指定范围内的文件
        filtered_files = []
        for file_path in all_files:
            try:
                step = int(file_path.split("第")[1].split("步")[0])
                if start_idx <= step <= end_idx:
                    filtered_files.append((step, file_path))
            except:
                continue
        
        # 按步骤排序
        filtered_files.sort(key=lambda x: x[0])
        
        # 构建相邻帧对，使用stride控制间隔
        for i in range(0, len(filtered_files) - stride, stride):
            if i + stride < len(filtered_files):
                self.file_pairs.append((filtered_files[i][1], filtered_files[i+stride][1]))
        
        print(f"找到 {len(self.file_pairs)} 对温度场数据，使用步长 {stride}")
        if downsample_factor > 1:
            print(f"使用降采样因子 {downsample_factor}，将 800×800 数据降采样至 {800//downsample_factor}×{800//downsample_factor}")
        print(f"温度场归一化范围: [{self.temp_min}, {self.temp_max}]")
    
    def __len__(self):
        return len(self.file_pairs)
    
    def __getitem__(self, idx):
        input_file, target_file = self.file_pairs[idx]
        
        # 使用缓存加速重复读取
        if self.use_cache and input_file in self.cache:
            input_data = self.cache[input_file]
        else:
            input_data = self._load_and_preprocess_csv(input_file)
            if self.use_cache:
                self.cache[input_file] = input_data
        
        if self.use_cache and target_file in self.cache:
            target_data = self.cache[target_file]
        else:
            target_data = self._load_and_preprocess_csv(target_file)
            if self.use_cache:
                self.cache[target_file] = target_data
        
        # 应用变换
        if self.transform:
            input_data = self.transform(input_data)
            target_data = self.transform(target_data)
        
        return input_data, target_data
    
    def _load_and_preprocess_csv(self, file_path):
        """加载CSV文件并进行预处理"""
        try:
            # 使用numpy加载数据
            data = np.loadtxt(file_path, delimiter=',')
            
            # 降采样处理
            if self.downsample_factor > 1:
                # 使用步长采样降低分辨率
                data = data[::self.downsample_factor, ::self.downsample_factor]
            
            # 数据归一化 - 使用固定的温度范围而不是每个文件单独归一化
            # 这样可以保留温度场的绝对值信息
            data = (data - self.temp_min) / (self.temp_max - self.temp_min)
            
            # 裁剪到[0,1]范围，避免极端值
            data = np.clip(data, 0.0, 1.0)
            
            # 转换为PyTorch张量
            data_tensor = torch.FloatTensor(data).unsqueeze(0)  # 添加通道维度 [1, H, W]
            
            return data_tensor
        except Exception as e:
            print(f"加载文件 {file_path} 时出错: {str(e)}")
            # 返回一个全零张量作为替代
            if self.downsample_factor > 1:
                size = 800 // self.downsample_factor
                return torch.zeros((1, size, size), dtype=torch.float32)
            else:
                return torch.zeros((1, 800, 800), dtype=torch.float32)
    
    def get_test_item_original_resolution(self, idx):
        """
        获取指定索引的测试项，以原始分辨率返回
        
        参数:
            idx (int): 数据索引
            
        返回:
            tuple: (input_data, target_data) 两个原始分辨率的张量
        """
        input_file, target_file = self.file_pairs[idx]
        
        try:
            # 直接加载原始分辨率数据，不降采样
            # 使用numpy加载数据
            input_data = np.loadtxt(input_file, delimiter=',')
            target_data = np.loadtxt(target_file, delimiter=',')
            
            # 数据归一化 - 使用固定的温度范围
            input_data = (input_data - self.temp_min) / (self.temp_max - self.temp_min)
            target_data = (target_data - self.temp_min) / (self.temp_max - self.temp_min)
            
            # 裁剪到[0,1]范围，避免极端值
            input_data = np.clip(input_data, 0.0, 1.0)
            target_data = np.clip(target_data, 0.0, 1.0)
            
            # 转换为PyTorch张量
            input_tensor = torch.FloatTensor(input_data).unsqueeze(0)  # [1, H, W]
            target_tensor = torch.FloatTensor(target_data).unsqueeze(0)  # [1, H, W]
            
            return input_tensor, target_tensor
        except Exception as e:
            print(f"加载原始分辨率测试数据出错: {str(e)}")
            return torch.zeros((1, 800, 800), dtype=torch.float32), torch.zeros((1, 800, 800), dtype=torch.float32)
    
    def clear_cache(self):
        """清除缓存"""
        self.cache.clear()

# 定义SpectralConv2d模块 - 这是FNO的核心组件
class SpectralConv2d(nn.Module):
    def __init__(self, in_channels, out_channels, modes1, modes2):
        super(SpectralConv2d, self).__init__()
        self.in_channels = in_channels
        self.out_channels = out_channels
        # 选择要保留的模式数量
        self.modes1 = modes1  # 第一个维度中的模式数
        self.modes2 = modes2  # 第二个维度中的模式数

        # 缩放系数
        self.scale = 1.0 / (in_channels * out_channels)
        
        # 创建权重参数 - 复数形式
        self.weights1_real = nn.Parameter(
            self.scale * torch.rand(in_channels, out_channels, self.modes1, self.modes2, dtype=torch.float32)
        )
        self.weights1_imag = nn.Parameter(
            self.scale * torch.rand(in_channels, out_channels, self.modes1, self.modes2, dtype=torch.float32)
        )
    
    def forward(self, x):
        batchsize = x.shape[0]
        size1 = x.shape[-2]
        size2 = x.shape[-1]
        
        # 应用FFT, 得到复数格式
        x_ft = torch.fft.rfft2(x)

        # 初始化输出频域张量
        out_ft = torch.zeros(batchsize, self.out_channels, size1, size2 // 2 + 1, 
                             device=x.device, dtype=torch.cfloat)
        
        # 对低频部分进行处理
        weights_complex = torch.complex(self.weights1_real, self.weights1_imag)
        
        # 限制处理的频域范围
        modes1 = min(self.modes1, size1)
        modes2 = min(self.modes2, size2 // 2 + 1)
        
        # 复数矩阵乘法
        out_ft[:, :, :modes1, :modes2] = torch.einsum(
            "bixy,ioxy->boxy", 
            x_ft[:, :, :modes1, :modes2], 
            weights_complex
        )

        # 应用逆FFT，转回空间域
        x = torch.fft.irfft2(out_ft, s=(size1, size2))
        return x

# 改进的FNO2d模型 - 针对温度场
class FNO2d(nn.Module):
    def __init__(self, modes1=14, modes2=14, hidden_channels=32, layers=4, in_channels=1, out_channels=1):
        super(FNO2d, self).__init__()
        self.modes1 = modes1
        self.modes2 = modes2
        self.hidden_channels = hidden_channels
        self.layers = layers
        self.in_channels = in_channels
        self.out_channels = out_channels
        
        # 将输入维度提升到隐藏维度
        self.fc0 = nn.Linear(in_channels, hidden_channels)
        
        # FNO层 - 包含多个频谱卷积和权重
        self.convs = nn.ModuleList()
        self.ws = nn.ModuleList()
        self.bn_layers = nn.ModuleList()
        self.activations = nn.ModuleList()
        
        for _ in range(layers):
            self.convs.append(SpectralConv2d(hidden_channels, hidden_channels, modes1, modes2))
            self.ws.append(nn.Conv2d(hidden_channels, hidden_channels, kernel_size=1))
            self.bn_layers.append(nn.BatchNorm2d(hidden_channels))
            self.activations.append(nn.GELU())
        
        # 输出层
        self.fc1 = nn.Linear(hidden_channels, 128)
        self.fc2 = nn.Linear(128, out_channels)
        self.dropout = nn.Dropout(0.1)  # 温度场特有: 增加Dropout避免过拟合
        
        # 深度残差连接系数
        self.res_scale = nn.Parameter(torch.ones(1))
        
        # 初始化权重
        self._reset_parameters()
    
    def _reset_parameters(self):
        """改进的权重初始化方法"""
        for m in self.modules():
            if isinstance(m, nn.Conv2d) or isinstance(m, nn.Linear):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.zeros_(m.bias)
    
    def forward(self, x):
        # 保存输入用于残差连接
        identity = x
        
        # 输入投影
        x = x.permute(0, 2, 3, 1)  # [B, H, W, C]
        x = self.fc0(x)
        x = x.permute(0, 3, 1, 2)  # [B, C, H, W]
        
        # 应用傅里叶层，添加层间残差连接
        x_skip = None
        for i in range(self.layers):
            # 频谱卷积
            x1 = self.convs[i](x)
            # 空间卷积
            x2 = self.ws[i](x)
            # 合并结果
            x_combined = x1 + x2
            # 批量归一化
            x_combined = self.bn_layers[i](x_combined)
            # 激活函数
            if i < self.layers - 1:
                x_combined = self.activations[i](x_combined)
            
            # 添加层间残差连接
            if i > 0 and x_skip is not None:
                x_combined = x_combined + self.res_scale * x_skip
            
            # 保存当前层输出用于下一层的残差连接
            x_skip = x_combined
            x = x_combined
        
        # 输出投影
        x = x.permute(0, 2, 3, 1)  # [B, H, W, C]
        x = self.activations[0](self.fc1(x))
        x = self.dropout(x)
        x = self.fc2(x)
        x = x.permute(0, 3, 1, 2)  # [B, C, H, W]
        
        # 全局残差连接
        x = x + self.res_scale * identity
        
        return x

# 针对温度场的自适应加权损失函数
class AdaptiveWeightedLoss(nn.Module):
    def __init__(self, alpha=0.7, beta=0.3):
        super(AdaptiveWeightedLoss, self).__init__()
        self.alpha = alpha
        self.beta = beta
        self.mse = nn.MSELoss(reduction='none')
        self.l1 = nn.L1Loss(reduction='none')
    
    def forward(self, pred, target):
        # 计算不同类型的损失
        mse_loss = self.mse(pred, target)
        l1_loss = self.l1(pred, target)
        
        # 计算梯度大小 - 温度场中的温度梯度区域需要重点关注
        target_grad_h = target[:, :, 1:, :] - target[:, :, :-1, :]
        target_grad_w = target[:, :, :, 1:] - target[:, :, :, :-1]
        target_grad = torch.sqrt(target_grad_h[:, :, :, :-1]**2 + target_grad_w[:, :, :-1, :]**2)
        
        # 创建权重掩码 - 梯度越大的区域权重越高
        weight_mask = torch.ones_like(target)
        
        # 填充权重掩码的内部区域 - 修复维度不匹配问题
        h, w = target.shape[2]-1, target.shape[3]-1
        grad_resized = F.interpolate(target_grad, size=(h, w), mode='bilinear', align_corners=False)
        weight_mask[:, :, :-1, :-1] = 1.0 + 2.0 * grad_resized
        
        # 结合两种损失，并应用权重掩码
        combined_loss = (self.alpha * mse_loss + self.beta * l1_loss) * weight_mask
        
        return combined_loss.mean()

# 添加一个新的函数用于每500步生成预测和真实值对比
def generate_multi_step_predictions(model, dataset, device, save_dir, downsample_factor=1, sample_stride=10, step_interval=200, specific_steps=None):
    """
    生成多个时间步的预测结果图，所有预测放在同一个图像中
    
    参数:
        model: 训练好的模型
        dataset: 数据集
        device: 计算设备
        save_dir: 保存目录
        downsample_factor: 降采样因子
        sample_stride: 采样步长
        step_interval: 时间步间隔，默认为200步
        specific_steps: 指定的时间步列表，如果提供则优先使用这些步数
    """
    print(f"生成多个时间步的预测结果图...")
    model.eval()
    
    # 获取数据集的总长度
    total_steps = len(dataset)
    
    # 确定要生成的时间步
    step_indices = []
    
    # 如果提供了specific_steps，则使用指定的步数
    if specific_steps and len(specific_steps) > 0:
        # 将指定的步数转换为数据集索引
        for step in specific_steps:
            # 确保步数在有效范围内
            if dataset.start_idx <= step <= dataset.end_idx:
                # 计算对应的数据集索引
                idx = (step - dataset.start_idx) // dataset.stride
                if 0 <= idx < total_steps:
                    step_indices.append(idx)
            else:
                print(f"警告: 指定的步数 {step} 超出有效范围 [{dataset.start_idx}, {dataset.end_idx}]")
    else:
        # 如果没有提供specific_steps，则按照step_interval间隔选择时间步
        for i in range(0, total_steps, step_interval):
            if i < total_steps:
                step_indices.append(i)
    
        # 如果最后一步不在列表中，添加它
        if (total_steps - 1) not in step_indices:
            step_indices.append(total_steps - 1)
    
    # 如果没有有效的时间步，则退出
    if not step_indices:
        print("错误: 没有找到有效的时间步进行预测")
        return
    
    # 限制最多显示7个时间步，避免图像过于拥挤
    if len(step_indices) > 7:
        print(f"警告: 指定了超过7个时间步，将只显示前7个")
        step_indices = step_indices[:7]
    
    # 计算图像布局
    n_steps = len(step_indices)
    n_cols = n_steps  # 每个时间步一列
    n_rows = 2  # 第一行是真实值，第二行是预测值
    
    # 创建一个大图
    plt.figure(figsize=(n_steps * 2, 5))
    plt.suptitle("Temperature Field Multi-Step Predictions", fontsize=16)
    
    # 获取温度范围
    try:
        temp_min, temp_max = dataset.temp_min, dataset.temp_max
    except:
        # 如果数据集没有温度范围，使用默认值
        temp_min, temp_max = 234.0, 253.0
    
    # 确定统一的颜色范围 - 使用实际温度值
    vmin_global = temp_min
    vmax_global = temp_max
    
    # 首先获取所有数据
    all_data = []
    with torch.no_grad():
        for idx in step_indices:
            input_data, target_data = dataset[idx]
            input_data = input_data.unsqueeze(0).to(device)  # 添加批次维度
            
            # 预测
            prediction = model(input_data)
            
            # 将张量移动到CPU
            prediction = prediction.cpu()
            target_data = target_data.cpu()
            
            # 获取numpy数组
            pred_np = prediction[0, 0].numpy()
            target_np = target_data[0].numpy()
            
            # 将归一化数据转换回实际温度值以便可视化
            pred_temp = pred_np * (temp_max - temp_min) + temp_min
            target_temp = target_np * (temp_max - temp_min) + temp_min
            
            # 计算当前时间步
            current_step = dataset.start_idx + idx * dataset.stride
            
            all_data.append((pred_temp, target_temp, current_step))
    
    # 创建一个共享的colorbar
    cmap = plt.cm.jet
    norm = plt.Normalize(vmin=vmin_global, vmax=vmax_global)
    
    # 绘制所有时间步的真实值和预测图
    for i, (pred_temp, target_temp, current_step) in enumerate(all_data):
        # 绘制真实值（第一行）
        plt.subplot(n_rows, n_cols, i + 1)
        im = plt.imshow(target_temp, cmap=cmap, norm=norm)
        plt.title(f"Step {current_step} (Ground Truth)", fontsize=8)
        plt.axis('off')
        
        # 绘制预测值（第二行）
        plt.subplot(n_rows, n_cols, i + 1 + n_cols)
        plt.imshow(pred_temp, cmap=cmap, norm=norm)
        plt.title(f"Step {current_step} (Prediction)", fontsize=8)
        plt.axis('off')
    
    # 添加一个共享的colorbar
    cbar_ax = plt.gcf().add_axes([0.92, 0.15, 0.02, 0.7])
    plt.colorbar(im, cax=cbar_ax)
    
    # 调整布局并保存
    plt.tight_layout(rect=[0, 0, 0.9, 0.95])  # 为顶部标题和右侧colorbar留出空间
    plt.savefig(os.path.join(save_dir, 'temperature_field_multi_step_predictions.png'), dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"多时间步预测图已保存至: {os.path.join(save_dir, 'temperature_field_multi_step_predictions.png')}")

# 修改train_model函数，删除对generate_periodic_prediction的调用
def train_model(model, train_loader, val_loader, device, num_epochs=10, 
                learning_rate=1e-3, save_dir='./fno_models', loss_weights=None,
                use_lr_scheduler=True, weight_decay=1e-4, early_stopping=0, data_noise=0.0):
    """
    训练FNO模型，使用优化的训练策略
    
    新增特性:
    - 自适应学习率调整
    - 内存优化
    - 高效的损失计算
    """
    # 创建保存目录
    os.makedirs(save_dir, exist_ok=True)

    # 定义损失函数
    mse_criterion = nn.MSELoss()
    l1_criterion = nn.L1Loss()
    
    # 设置默认损失权重
    if loss_weights is None:
        loss_weights = {
            'mse': 0.7,  # 温度场更适合MSE损失
            'l1': 0.3
        }

    # 优化器
    optimizer = torch.optim.AdamW(model.parameters(), lr=learning_rate, weight_decay=weight_decay)

    # 学习率调度器
    if use_lr_scheduler:
        scheduler = torch.optim.lr_scheduler.OneCycleLR(
            optimizer, max_lr=learning_rate, 
            steps_per_epoch=len(train_loader), 
            epochs=num_epochs,
            pct_start=0.3,  # 花30%的时间预热
            div_factor=25.0,  # 初始学习率为max_lr/25
            final_div_factor=1000.0  # 最终学习率为max_lr/1000
        )
    else:
        scheduler = None

    # 记录训练和验证损失
    train_losses = []
    val_losses = []
    best_val_loss = float('inf')
    early_stop_counter = 0
    
    # 监控训练进度
    print(f"开始训练温度场模型，总轮次: {num_epochs}，总批次: {len(train_loader)}")
    print(f"学习率: {learning_rate}，批大小: {train_loader.batch_size}")
    
    # 训练循环
    for epoch in range(num_epochs):
        model.train()
        train_loss = 0.0
        epoch_start_time = time.time()
        
        # 训练一个epoch
        for batch_idx, (inputs, targets) in enumerate(tqdm(train_loader, desc=f"Epoch {epoch+1}/{num_epochs} [Train]")):
            # 将数据移动到设备
            inputs, targets = inputs.to(device, non_blocking=True), targets.to(device, non_blocking=True)

            # 添加噪声到训练数据，提高泛化能力
            if data_noise > 0:
                noise = torch.randn_like(inputs) * data_noise
                inputs = inputs + noise
                inputs = torch.clamp(inputs, 0.0, 1.0)
                
            # 清零梯度
            optimizer.zero_grad(set_to_none=True)

            # 前向传播
            outputs = model(inputs)

            # 计算损失 - 使用简化的损失计算
            mse_loss = mse_criterion(outputs, targets)
            l1_loss = l1_criterion(outputs, targets)
            
            # 组合损失
            loss = loss_weights['mse'] * mse_loss + loss_weights['l1'] * l1_loss
            
            # 反向传播
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            
            # 更新参数
            optimizer.step()
            
            # 更新学习率
            if scheduler:
                scheduler.step()
                
            # 记录损失
            train_loss += loss.item()
            
            # 定期释放未使用的内存
            if torch.cuda.is_available() and (batch_idx + 1) % 50 == 0:
                torch.cuda.empty_cache()

        # 计算平均训练损失
        avg_train_loss = train_loss / len(train_loader)
        train_losses.append(avg_train_loss)

        # 验证
        model.eval()
        val_loss = 0.0
        with torch.no_grad():
            for inputs, targets in tqdm(val_loader, desc=f"Epoch {epoch+1}/{num_epochs} [Val]"):
                inputs, targets = inputs.to(device, non_blocking=True), targets.to(device, non_blocking=True)
                outputs = model(inputs)

                # 计算验证损失
                loss = mse_criterion(outputs, targets)
                val_loss += loss.item()

        # 计算平均验证损失
        avg_val_loss = val_loss / len(val_loader)
        val_losses.append(avg_val_loss)

        # 输出训练信息
        epoch_time = time.time() - epoch_start_time
        print(f"Epoch {epoch+1}/{num_epochs}, Train Loss: {avg_train_loss:.6f}, "
              f"Val Loss: {avg_val_loss:.6f}, Time: {epoch_time:.2f}s")
        
        # 每10轮保存一次损失曲线
        if (epoch + 1) % 10 == 0:
            plot_loss_curve(train_losses, val_losses, save_dir)

        # 保存最佳模型
        if avg_val_loss < best_val_loss:
            best_val_loss = avg_val_loss
            torch.save(model.state_dict(), os.path.join(save_dir, "temperature_field_model.pth"), _use_new_zipfile_serialization=True)
            print(f"Saved new best model with validation loss: {best_val_loss:.6f}")
            early_stop_counter = 0
        else:
            early_stop_counter += 1

        # 提前停止
        if early_stopping > 0 and early_stop_counter >= early_stopping:
            print(f"提前停止训练: 验证损失在{early_stopping}轮内未改善")
            break

    # 绘制最终损失曲线
    plot_loss_curve(train_losses, val_losses, save_dir)
    
    # 返回训练历史
    history = {
        'train_loss': train_losses,
        'val_loss': val_losses
    }
    return history

# 添加新的绘图函数，使损失曲线更美观
def plot_loss_curve(train_losses, val_losses, save_dir):
    """绘制更美观的损失曲线"""
    plt.figure(figsize=(10, 6))
    epochs = range(1, len(train_losses) + 1)
    
    plt.plot(epochs, train_losses, 'b-', label='Training Loss', linewidth=2)
    plt.plot(epochs, val_losses, 'r-', label='Validation Loss', linewidth=2)
    
    plt.title('Temperature Field - Training and Validation Loss', fontsize=14)
    plt.xlabel('Epochs', fontsize=12)
    plt.ylabel('Loss', fontsize=12)
    plt.yscale('log')  # 使用对数尺度更好地显示损失变化
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.legend(fontsize=12)
    
    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'temperature_field_loss.png'), dpi=300, bbox_inches='tight')
    plt.close()

# 修改预测可视化函数，增强可视化效果
def predict_and_visualize(model, test_input, test_target, device, save_dir='./fno_models', downsample_factor=1, end_idx=3000, sample_stride=10):
    """
    预测并可视化结果，使用改进的可视化效果
    """
    model.eval()
    with torch.no_grad():
        # 确保输入数据在正确的设备上
        test_input = test_input.to(device)
        
        # 预测
        start_time = time.time()
        prediction = model(test_input)
        inference_time = time.time() - start_time
        
        # 如果使用了降采样，需要将预测结果恢复到原始分辨率
        if downsample_factor > 1:
            # 使用三次插值将预测结果上采样回原始分辨率
            # 获取目标尺寸
            target_size = test_target.shape[-2:]  # 获取高度和宽度
            prediction = F.interpolate(
                prediction, 
                size=target_size, 
                mode='bicubic', 
                align_corners=False
            )
            print(f"上采样后的预测形状: {prediction.shape}, 目标形状: {test_target.shape}")
        
        # 将张量移动到CPU以便可视化
        test_input = test_input.cpu()
        test_target = test_target.cpu()
        prediction = prediction.cpu()
        
        # 使用原始数据进行可视化
        test_target_np = test_target[0, 0].numpy()
        prediction_np = prediction[0, 0].numpy()
        
        # 验证形状是否匹配
        if test_target_np.shape != prediction_np.shape:
            print(f"警告: 形状不匹配 - 目标: {test_target_np.shape}, 预测: {prediction_np.shape}")
            # 确保形状匹配，如果不匹配则调整预测大小
            prediction_np = np.resize(prediction_np, test_target_np.shape)
        
        # 计算并打印最大误差
        error = np.abs(test_target_np - prediction_np)
        max_error = np.max(error)
        mean_error = np.mean(error)
        print(f"最大绝对误差: {max_error:.6f}, 平均绝对误差: {mean_error:.6f}")
        
        # 获取数据集以获取温度范围
        dataset = TemperatureFieldDataset(os.path.join(os.path.dirname(save_dir), "TemperatureField_Data"))
        temp_min, temp_max = dataset.temp_min, dataset.temp_max
        
        # 增强的可视化 - 创建更美观的图形
        plt.figure(figsize=(18, 7))
        plt.suptitle("Temperature Field Prediction", fontsize=18, y=0.98)
        
        # 将归一化数据转换回实际温度值以便可视化
        test_target_temp = test_target_np * (temp_max - temp_min) + temp_min
        prediction_temp = prediction_np * (temp_max - temp_min) + temp_min
        
        # 确定统一的颜色范围 - 使用实际温度值
        vmin = temp_min
        vmax = temp_max
        
        # 绘制真实值
        plt.subplot(1, 3, 1)
        im1 = plt.imshow(test_target_temp, cmap='jet', vmin=vmin, vmax=vmax)  # 使用jet更适合温度场
        plt.colorbar(im1, fraction=0.046, pad=0.04)
        plt.title(f"Ground Truth (Step {end_idx})", fontsize=14)
        plt.axis('off')
        
        # 绘制预测值
        plt.subplot(1, 3, 2)
        im2 = plt.imshow(prediction_temp, cmap='jet', vmin=vmin, vmax=vmax)
        plt.colorbar(im2, fraction=0.046, pad=0.04)
        plt.title("Model Prediction", fontsize=14)
        plt.axis('off')
        
        # 绘制误差图 - 使用viridis色彩图更清晰地显示误差
        plt.subplot(1, 3, 3)
        # 将误差转换为实际温度单位
        error_temp = error * (temp_max - temp_min)
        error_plot = plt.imshow(error_temp, cmap='inferno', vmin=0, vmax=max_error*(temp_max-temp_min))
        plt.colorbar(error_plot, fraction=0.046, pad=0.04)
        plt.title(f"Absolute Error (Max: {max_error*(temp_max-temp_min):.4f}K)", fontsize=14)
        plt.axis('off')
        
        # 保存图形
        plt.tight_layout()
        plt.savefig(os.path.join(save_dir, 'temperature_field_prediction.png'), dpi=300, bbox_inches='tight')
        plt.close()
        
        # 额外保存一个原始图像对比
        plt.figure(figsize=(12, 6))
        
        # 绘制测试输入（前一帧）
        plt.subplot(1, 2, 1)
        input_temp = test_input[0, 0].numpy() * (temp_max - temp_min) + temp_min
        plt.imshow(input_temp, cmap='jet', vmin=vmin, vmax=vmax)
        plt.colorbar()
        plt.title(f"Input Frame (Step {end_idx-sample_stride})")
        plt.axis('off')
        
        # 绘制真实的下一帧
        plt.subplot(1, 2, 2)
        plt.imshow(test_target_temp, cmap='jet', vmin=vmin, vmax=vmax)
        plt.colorbar()
        plt.title(f"True Next Frame (Step {end_idx})")
        plt.axis('off')
        
        plt.tight_layout()
        plt.savefig(os.path.join(save_dir, 'temperature_field_frames.png'), dpi=300, bbox_inches='tight')
        plt.close()
        
        # 计算评估指标
        mse = F.mse_loss(prediction, test_target).item()
        mae = F.l1_loss(prediction, test_target).item()
        
        # 计算结构相似性指数 (SSIM)
        try:
            from skimage.metrics import structural_similarity as ssim
            # 转换为numpy并调整维度
            pred_np = prediction[0, 0].numpy()
            target_np = test_target[0, 0].numpy()
            # 计算SSIM
            ssim_value = ssim(pred_np, target_np, data_range=1.0)
        except ImportError:
            ssim_value = 0.0
            print("未安装scikit-image，无法计算SSIM")
        
        # 保存评估指标
        metrics = {
            'mse': mse,
            'mae': mae,
            'ssim': ssim_value,
            'max_error': float(max_error),
            'mean_error': float(mean_error),
            'inference_time_ms': inference_time * 1000
        }
        
        with open(os.path.join(save_dir, 'temperature_evaluation_metrics.json'), 'w') as f:
            json.dump(metrics, f, indent=4)
        
        print(f"预测完成. MSE: {mse:.6f}, MAE: {mae:.6f}, SSIM: {ssim_value:.4f}")
        print(f"推理时间: {inference_time*1000:.2f} ms")
        
        return prediction, metrics

# 使用块处理大型数据的辅助函数
def process_in_patches(model, input_tensor, patch_size=128, overlap=16, device=None):
    """将大型张量分成小块进行处理，然后重组"""
    if device is None:
        device = next(model.parameters()).device
    
    # 获取输入张量的形状
    B, C, H, W = input_tensor.shape
    
    # 计算步长（考虑重叠）
    stride = patch_size - overlap
    
    # 计算需要的patch数量（考虑重叠）
    num_patches_h = max(1, (H - overlap) // stride)
    num_patches_w = max(1, (W - overlap) // stride)
    
    # 创建输出张量
    output_tensor = torch.zeros_like(input_tensor)
    weight_mask = torch.zeros_like(input_tensor)
    
    # 为了平滑边界，创建权重掩码
    patch_weight = torch.ones((1, C, patch_size, patch_size), device=device)
    
    # 边缘权重衰减
    for i in range(overlap):
        # 上下左右边缘的权重衰减
        weight = i / overlap
        patch_weight[:, :, i, :] *= weight  # 顶部边缘
        patch_weight[:, :, patch_size - i - 1, :] *= weight  # 底部边缘
        patch_weight[:, :, :, i] *= weight  # 左侧边缘
        patch_weight[:, :, :, patch_size - i - 1] *= weight  # 右侧边缘
    
    # 分块处理
    for h_idx in range(num_patches_h):
        for w_idx in range(num_patches_w):
            # 计算当前块的位置
            h_start = h_idx * stride
            w_start = w_idx * stride
            h_end = min(h_start + patch_size, H)
            w_end = min(w_start + patch_size, W)
            
            # 提取当前块
            h_slice = slice(h_start, h_end)
            w_slice = slice(w_start, w_end)
            input_patch = input_tensor[:, :, h_slice, w_slice]
            
            # 处理当前块的大小
            current_h, current_w = input_patch.shape[2], input_patch.shape[3]
            
            # 如果块大小不足，需要填充
            if current_h < patch_size or current_w < patch_size:
                padded_patch = torch.zeros((B, C, patch_size, patch_size), device=device)
                padded_patch[:, :, :current_h, :current_w] = input_patch
                input_patch = padded_patch
            
            # 将块移动到设备并进行预测
            input_patch = input_patch.to(device)
            
            # 模型预测
            with torch.no_grad():
                output_patch = model(input_patch)
            
            # 截取回原始大小（如果之前进行了填充）
            output_patch = output_patch[:, :, :current_h, :current_w]
            current_weight = patch_weight[:, :, :current_h, :current_w]
            
            # 将预测结果加权累加到输出张量
            output_tensor[:, :, h_slice, w_slice] += output_patch * current_weight
            weight_mask[:, :, h_slice, w_slice] += current_weight
    
    # 归一化输出（基于权重）
    weight_mask = torch.clamp(weight_mask, min=1e-6)
    output_tensor = output_tensor / weight_mask
    
    return output_tensor

def load_image_as_tensor(image_path):
    """
    加载PNG图像作为PyTorch张量
    
    参数:
        image_path (str): 图像文件路径
        
    返回:
        torch.Tensor: [1, H, W] 形状的张量
    """
    try:
        # 加载图像
        img = Image.open(image_path)
        
        # 转换为灰度图
        if img.mode != 'L':
            img = img.convert('L')
            
        # 转换为numpy数组
        img_array = np.array(img).astype(np.float32)
        
        # 获取数据集以获取温度范围
        dataset = TemperatureFieldDataset(os.path.dirname(os.path.dirname(image_path)))
        temp_min, temp_max = dataset.temp_min, dataset.temp_max
        
        # 根据图像的实际值范围进行归一化
        # 假设图像是0-255的灰度图，需要映射到温度范围
        img_array = (img_array / 255.0) * (temp_max - temp_min) + temp_min
        
        # 再归一化到[0,1]范围
        img_array = (img_array - temp_min) / (temp_max - temp_min)
        
        # 裁剪到[0,1]范围，避免极端值
        img_array = np.clip(img_array, 0.0, 1.0)
        
        # 转换为PyTorch张量并添加通道维度
        img_tensor = torch.FloatTensor(img_array).unsqueeze(0)
        
        print(f"成功加载图像: {image_path}, 形状: {img_tensor.shape}, 值范围: [{img_tensor.min():.4f}, {img_tensor.max():.4f}]")
        
        return img_tensor
    except Exception as e:
        print(f"加载图像 {image_path} 时出错: {str(e)}")
        return torch.zeros((1, 800, 800), dtype=torch.float32)

def main():
    parser = argparse.ArgumentParser(description='FNO温度场模拟训练')
    parser.add_argument('--gpu_id', type=int, default=0, help='使用的GPU ID')
    parser.add_argument('--no_cuda', action='store_true', help='不使用CUDA (即使可用)')
    parser.add_argument('--data_dir', type=str, default='./TemperatureField_Data', help='数据目录路径')
    parser.add_argument('--image_dir', type=str, default='./TemperatureField_Images', help='图像目录路径，用于加载真实值')
    parser.add_argument('--start_idx', type=int, default=0, help='数据起始帧索引')
    parser.add_argument('--end_idx', type=int, default=3050, help='数据结束帧索引')
    parser.add_argument('--num_epochs', type=int, default=10, help='训练轮数')
    parser.add_argument('--batch_size', type=int, default=4, help='批次大小')
    parser.add_argument('--save_dir', type=str, default='./fno_models/temp', help='模型保存目录')
    parser.add_argument('--num_workers', type=int, default=4, help='数据加载的工作进程数')
    parser.add_argument('--modes', type=int, default=14, help='傅里叶模式数量')
    parser.add_argument('--width', type=int, default=32, help='网络宽度')
    parser.add_argument('--use_cache', action='store_true', default=True, help='使用数据缓存')
    parser.add_argument('--learning_rate', type=float, default=1e-3, help='学习率')
    parser.add_argument('--sample_stride', type=int, default=2, help='样本帧间步长')
    parser.add_argument('--use_png_groundtruth', action='store_true', help='使用PNG图像作为真实值而不是CSV数据')
    parser.add_argument('--downsample_factor', type=int, default=4, help='空间降采样因子,值为4时将800×800降采样至200×200')
    parser.add_argument('--weight_decay', type=float, default=1e-4, help='权重衰减(L2正则化)')
    parser.add_argument('--dropout', type=float, default=0.0, help='Dropout概率, 增加可以防止过拟合')
    parser.add_argument('--accumulation_steps', type=int, default=2, help='梯度累积步数，可以使用更大的等效批量')
    parser.add_argument('--plot_freq', type=int, default=10, help='每多少轮保存一次损失曲线')
    parser.add_argument('--pin_memory', action='store_true', default=True, help='使用pin_memory加速数据传输')
    parser.add_argument('--persistent_workers', action='store_true', default=True, help='使用持久化工作进程加速数据加载')
    parser.add_argument('--mode', type=str, default='train', choices=['train', 'predict', 'retrain'], 
                       help='运行模式: train-从头训练新模型, predict-仅使用现有模型预测, retrain-加载现有模型继续训练')
    parser.add_argument('--force_model_size', action='store_true', default=False,
                       help='当使用predict或retrain模式时，是否强制使用命令行指定的模型尺寸。若为False，则自动适配已保存模型的尺寸')
    # 添加新的参数用于指定具体的时间步
    parser.add_argument('--specific_steps', type=int, nargs='+', default=[100,500,1000,1500,2000,2500],
                       help='指定要生成预测的时间步列表，例如 "--specific_steps 100 500 1000 1500 2000 2500"')
    parser.add_argument('--step_interval', type=int, default=200, 
                       help='如果不指定具体时间步，则使用此间隔自动选择时间步')
    args = parser.parse_args()
    
    # 设置设备
    if not args.no_cuda and torch.cuda.is_available():
        device = torch.device(f"cuda:{args.gpu_id}")
    else:
        device = torch.device("cpu")
    print(f"使用设备: {device}")
    
    # 创建保存目录
    os.makedirs(args.save_dir, exist_ok=True)
    
    # 确保数据目录存在
    if not os.path.exists(args.data_dir):
        print(f"警告: 数据目录 {args.data_dir} 不存在")
        # 尝试查找可能的数据目录
        possible_dirs = glob.glob("*Temperature*") + glob.glob("*temperature*") + glob.glob("*temp*")
        if possible_dirs:
            print(f"找到可能的数据目录: {possible_dirs}")
            for dir_name in possible_dirs:
                if os.path.isdir(dir_name) and glob.glob(os.path.join(dir_name, "温度场第*步.csv")):
                    args.data_dir = dir_name
                    print(f"使用找到的数据目录: {args.data_dir}")
                    break
    
    # 加载数据集
    dataset = TemperatureFieldDataset(
        args.data_dir, 
        start_idx=args.start_idx, 
        end_idx=args.end_idx, 
        use_cache=args.use_cache,
        stride=args.sample_stride,
        downsample_factor=args.downsample_factor
    )
    
    # 划分训练集和验证集
    train_size = int(0.8 * len(dataset))
    val_size = len(dataset) - train_size
    train_dataset, val_dataset = torch.utils.data.random_split(dataset, [train_size, val_size])
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset, 
        batch_size=args.batch_size, 
        shuffle=True,
        num_workers=args.num_workers, 
        pin_memory=args.pin_memory,
        persistent_workers=args.persistent_workers if args.num_workers > 0 else False,
        drop_last=False
    )
    val_loader = DataLoader(
        val_dataset, 
        batch_size=args.batch_size, 
        shuffle=False,
        num_workers=args.num_workers, 
        pin_memory=args.pin_memory,
        persistent_workers=args.persistent_workers if args.num_workers > 0 else False,
        drop_last=False
    )

    # 根据运行模式决定模型参数
    model_path = os.path.join(args.save_dir, "temperature_field_model.pth")
    model_exists = os.path.exists(model_path)
    
    # 检查模型文件是否存在
    if (args.mode == 'predict' or args.mode == 'retrain') and not model_exists:
        print(f"警告: 模型文件 {model_path} 不存在，将切换到train模式从头开始训练")
        args.mode = 'train'
    
    # 如果需要加载现有模型，先检查模型尺寸
    saved_modes = args.modes
    saved_width = args.width
    
    if args.mode != 'train' and model_exists and not args.force_model_size:
        try:
            # 临时加载模型以获取参数尺寸
            state_dict = torch.load(model_path, map_location='cpu')
            
            # 从模型权重推断尺寸
            if 'fc0.weight' in state_dict:
                saved_width = state_dict['fc0.weight'].shape[0]
                print(f"检测到已保存模型宽度: {saved_width}")
            
            if 'convs.0.weights1_real' in state_dict:  # 修正权重名称
                saved_modes = state_dict['convs.0.weights1_real'].shape[2]
                print(f"检测到已保存模型模式数: {saved_modes}")
                
            if saved_width != args.width or saved_modes != args.modes:
                print(f"注意: 调整模型参数以匹配已保存模型 (modes: {saved_modes}, width: {saved_width})")
        except Exception as e:
            print(f"读取模型参数时出错: {str(e)}，将使用命令行指定的参数")
    
    # 创建模型
    model = FNO2d(
        modes1=saved_modes,
        modes2=saved_modes,
        hidden_channels=saved_width,
        layers=4,
        in_channels=1,
        out_channels=1
    ).to(device)
    
    # 打印模型参数数量和配置信息
    total_params = sum(p.numel() for p in model.parameters())
    print(f"\n====== 温度场模型训练配置 ======")
    print(f"- 运行模式: {args.mode}")
    print(f"- 模型参数: {total_params:,}个参数, 模式数={saved_modes}, 宽度={saved_width}, 层数={4}")
    print(f"- 训练参数: 批大小={args.batch_size}, 学习率={args.learning_rate}")
    print(f"- 数据参数: 降采样因子={args.downsample_factor}, 样本步长={args.sample_stride}")
    print(f"- 训练设备: {device}")
    
    # 根据模式加载模型或训练模型
    if args.mode == 'predict' and model_exists:
        print(f"加载已有模型用于预测: {model_path}")
        model.load_state_dict(torch.load(model_path, map_location=device))
    elif args.mode == 'retrain' and model_exists:
        print(f"加载已有模型继续训练: {model_path}")
        model.load_state_dict(torch.load(model_path, map_location=device))
        
        # 继续训练模型
        history = train_model(
            model=model,
            train_loader=train_loader,
            val_loader=val_loader,
            device=device,
            num_epochs=args.num_epochs,
            learning_rate=args.learning_rate,
            save_dir=args.save_dir
        )
    elif args.mode == 'train' or not model_exists:
        print(f"从头开始训练新模型")
        
        # 训练模型
        history = train_model(
            model=model,
            train_loader=train_loader,
            val_loader=val_loader,
            device=device,
            num_epochs=args.num_epochs,
            learning_rate=args.learning_rate,
            save_dir=args.save_dir
        )
    
    # 获取一个测试样本
    test_idx = len(dataset) - 1
    test_input, test_target = dataset.get_test_item_original_resolution(test_idx)
    test_input = test_input.unsqueeze(0)  # 添加批次维度
    test_target = test_target.unsqueeze(0)  # 添加批次维度
    
    # 准备真实值数据
    if args.use_png_groundtruth:
        # 构建PNG图像路径 - 使用end_idx作为测试步数
        png_path = os.path.join(args.image_dir, f"温度场第{args.end_idx}步.png")
        if os.path.exists(png_path):
            print(f"从PNG图像加载真实值: {png_path}")
            # 加载PNG图像作为真实值
            ground_truth = load_image_as_tensor(png_path)
            ground_truth = ground_truth.unsqueeze(0)  # 添加批次维度
            
            print(f"测试数据形状 - 输入: {test_input.shape}, PNG真实值: {ground_truth.shape}")
            
            # 使用降采样的输入进行预测，自动上采样回原始分辨率
            prediction, metrics = predict_and_visualize(
                model=model,
                test_input=test_input,
                test_target=ground_truth,
                device=device,
                save_dir=args.save_dir,
                downsample_factor=args.downsample_factor,
                end_idx=args.end_idx,
                sample_stride=args.sample_stride
            )
        else:
            print(f"警告: PNG图像不存在: {png_path}，使用CSV数据作为真实值")
            # 获取原始分辨率数据用于评估
            original_input, original_target = dataset.get_test_item_original_resolution(test_idx)
            original_input = original_input.unsqueeze(0)  # 添加批次维度
            original_target = original_target.unsqueeze(0)  # 添加批次维度
            
            print(f"测试数据形状 - 降采样: {test_input.shape}, 原始: {original_input.shape}")
            
            # 预测并可视化结果
            prediction, metrics = predict_and_visualize(
                model=model, 
                test_input=test_input, 
                test_target=original_target,
                device=device, 
                save_dir=args.save_dir, 
                downsample_factor=args.downsample_factor,
                end_idx=args.end_idx,
                sample_stride=args.sample_stride
            )
    else:
        # 使用原始CSV数据作为真实值
        # 获取原始分辨率数据用于评估
        original_input, original_target = dataset.get_test_item_original_resolution(test_idx)
        original_input = original_input.unsqueeze(0)  # 添加批次维度
        original_target = original_target.unsqueeze(0)  # 添加批次维度
        
        print(f"测试数据形状 - 降采样: {test_input.shape}, 原始: {original_input.shape}")
        
        # 预测并可视化结果
        prediction, metrics = predict_and_visualize(
            model=model, 
            test_input=test_input, 
            test_target=original_target,
            device=device, 
            save_dir=args.save_dir, 
            downsample_factor=args.downsample_factor,
            end_idx=args.end_idx,
            sample_stride=args.sample_stride
        )
    
    # 训练完成
    if args.mode == 'predict' or args.mode == 'retrain' or args.mode == 'train':
        # 生成多个时间步的预测结果与真实值对比图
        generate_multi_step_predictions(
            model=model,
            dataset=dataset,
            device=device,
            save_dir=args.save_dir,
            downsample_factor=args.downsample_factor,
            sample_stride=args.sample_stride,
            step_interval=args.step_interval,  # 使用命令行参数指定的间隔
            specific_steps=args.specific_steps  # 使用命令行参数指定的具体时间步
        )

    print("温度场模型训练和评估完成！")

if __name__ == "__main__":
    main() 