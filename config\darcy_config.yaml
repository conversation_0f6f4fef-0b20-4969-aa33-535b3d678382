default: &DEFAULT

  #General
  # For computing compression
  n_params_baseline: None
  verbose: True
  arch: 'tfno2d'

  #Distributed computing
  distributed:
    use_distributed: False
    wireup_info: 'mpi'
    wireup_store: 'tcp'
    model_parallel_size: 2
    seed: 666

  # FNO related
  tfno2d:
    data_channels: 1
    n_modes_height: 16
    n_modes_width: 16
    hidden_channels: 32
    projection_channel_ratio: 2
    n_layers: 4
    domain_padding: None #0.078125
    domain_padding_mode: 'one-sided' #symmetric
    fft_norm: 'forward'
    norm: 'group_norm'
    skip: 'linear'
    implementation: 'factorized'
    separable: 0
    preactivation: 0
    use_channel_mlp: 1
    channel_mlp_expansion: 0.5
    channel_mlp_dropout: 0
    factorization: None
    rank: 1.0
    fixed_rank_modes: None
    dropout: 0.0
    tensor_lasso_penalty: 0.0
    joint_factorization: False
    stabilizer: None # or 'tanh'

  # Optimizer
  opt:
    n_epochs: 300
    learning_rate: 5e-3
    training_loss: 'h1'
    weight_decay: 1e-4
    amp_autocast: False

    scheduler_T_max: 500 # For cosine only, typically take n_epochs
    scheduler_patience: 5 # For ReduceLROnPlateau only
    scheduler: 'StepLR' # Or 'CosineAnnealingLR' OR 'ReduceLROnPlateau'
    step_size: 60
    gamma: 0.5

  # Dataset related
  data:
    batch_size: 16
    n_train: 1000
    train_resolution: 16
    n_tests: [100, 50]
    test_resolutions: [16, 32]
    test_batch_sizes: [16, 16]
    encode_input: True
    encode_output: False

  # Patching
  patching:
    levels: 0
    padding: 0
    stitching: False

  # Weights and biases
  wandb:
    log: False
    name: None # If None, config will be used but you can override it here
    group: '' 
    project: ""
    entity: "" # put your username here
    sweep: False
    log_output: True
    eval_interval: 1
