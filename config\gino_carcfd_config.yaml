cfd: &CFD

  arch: 'gino'
  sample_max: 5000

  # Distributed computing
  distributed:
    use_distributed: False
    wireup_info: 'mpi'
    wireup_store: 'tcp'
    model_parallel_size: 2
    seed: 666
    device: 'cuda:0'

  # Dataset related
  data:
    root: /home/<USER>/data/car-pressure-data/
    sdf_query_resolution: 32
    n_train: 500
    n_test: 111
    download: True

  gino:
    data_channels: 0
    out_channels: 1
    latent_feature_channels: 1
    gno_coord_dim: 3
    gno_coord_embed_dim: 16
    gno_radius: 0.033
    in_gno_transform_type: 'linear' # linear_kernelonly, linear, nonlinear_kernelonly, nonlinear
    out_gno_transform_type: 'linear' # linear_kernelonly, linear, nonlinear_kernelonly, nonlinear
    gno_pos_embed_type: 'nerf'
    fno_n_modes: [16, 16, 16]
    fno_hidden_channels: 64
    fno_use_channel_mlp: True
    fno_norm: 'instance_norm'
    fno_ada_in_features: 32
    fno_factorization: 'tucker'
    fno_rank: 0.4
    fno_domain_padding: 0.125
    fno_channel_mlp_expansion: 1.0
    fno_resolution_scaling_factor: 1

  opt:
    n_epochs: 301
    learning_rate: 1e-3
    training_loss: 'l2' 
    testing_loss: 'l2' 
    weight_decay: 1e-4
    amp_autocast: False

    scheduler_T_max: 500 # For cosine only, typically take n_epochs
    scheduler_patience: 5 # For ReduceLROnPlateau only
    scheduler: 'StepLR' # Or 'CosineAnnealingLR' OR 'ReduceLROnPlateau'
    step_size: 50
    gamma: 0.5

  # Weights and biases
  wandb:
    log: False #True
    name: None # If None, config will be used but you can override it here
    group: 'drag' 
    project: ""
    entity: ""
    sweep: False
    log_output: True
    eval_interval: 1