default: & DEFAULT  # 默认配置，&DEFAULT创建一个锚点，可以被其他配置引用

# General  # 通用设置
# For computing compression
n_params_baseline: None  # If None, will be computed  # 用于计算压缩率的基准参数数量，如果为None则自动计算
verbose: True  # 是否显示详细输出信息
arch: 'fno'  # 使用的架构类型，这里是傅里叶神经算子(Fourier Neural Operator)

# Distributed computing  # 分布式计算设置
distributed:
use_distributed: False  # 是否使用分布式计算
wireup_info: 'mpi'  # 分布式通信方式，这里使用MPI
wireup_store: 'tcp'  # 分布式存储方式，使用TCP协议
model_parallel_size: 2  # 模型并行的大小
seed: 666  # 随机种子，保证实验可重复性

# FNO related  # FNO（傅里叶神经算子）相关设置
fno:
data_channels: 1  # 输入数据的通道数
out_channels: 1  # 输出数据的通道数
n_modes: [64, 64]  # 傅里叶模式数量，针对二维数据的每个维度
hidden_channels: 64  # 隐藏层通道数
projection_channel_ratio: 4  # 投影通道比例
n_layers: 4  # FNO层数
domain_padding: 0.  # 0.078125  # 域填充大小，目前为0（不填充）
domain_padding_mode: 'one-sided'  # symmetric  # 域填充模式，单侧填充
fft_norm: 'forward'  # 傅里叶变换的归一化方式
norm: None  # 是否使用归一化层
skip: 'linear'  # 跳跃连接的类型，这里是线性连接
implementation: 'reconstructed'  # FNO的实现方式

use_channel_mlp: 1  # 是否使用通道MLP
channel_mlp_expansion: 0.5  # 通道MLP的扩展比例
channel_mlp_dropout: 0  # 通道MLP的dropout率

separable: False  # 是否使用可分离卷积
factorization: None  # 权重矩阵分解方法
rank: 1.0  # 低秩近似的秩
fixed_rank_modes: None  # 固定秩的模式
dropout: 0.0  # 模型中的dropout率
tensor_lasso_penalty: 0.0  # 张量LASSO惩罚系数
joint_factorization: False  # 是否使用联合因子分解
stabilizer: None  # or 'tanh'  # 稳定器类型，可选tanh

# Optimizer  # 优化器设置
opt:
n_epochs: 100  # 训练轮数
learning_rate: 3e-4  # 学习率
training_loss: 'h1'  # 训练损失函数类型，这里使用H1损失
weight_decay: 1e-4  # 权重衰减系数
amp_autocast: False  # 是否使用混合精度训练

scheduler_T_max: 500  # For cosine only, typically take n_epochs  # 余弦学习率调度器的最大周期
scheduler_patience: 50  # For ReduceLROnPlateau only  # ReduceLROnPlateau调度器的耐心值
scheduler: 'StepLR'  # Or 'CosineAnnealingLR' OR 'ReduceLROnPlateau'  # 学习率调度器类型
step_size: 100  # StepLR调度器的步长
gamma: 0.5  # 学习率调整的乘数因子

# Dataset related  # 数据集相关设置
data:
folder: data / navier_stokes /  # 数据集文件夹路径
batch_size: 8  # 训练批量大小
n_train: 1000  # 训练样本数量
train_resolution: 128  # 训练数据分辨率
n_tests: [200]  # , 1000] #, 1000]  # 测试样本数量
test_resolutions: [128]  # , 1024] #, 1024]  # 测试数据分辨率
test_batch_sizes: [8]  # , 4] #, 1]  # 测试批量大小
encode_input: True  # 是否对输入进行编码
encode_output: True  # 是否对输出进行编码

# Patching  # 图像块处理设置
patching:
levels: 0  # 1  # 多网格级别，0表示不使用多网格方法
padding: 0  # 0.078125  # 图像块填充比例
stitching: False  # True  # 是否拼接图像块

# Weights and biases  # Weights & Biases实验跟踪设置
wandb:
log: False  # 是否记录到W&B平台
name: None  # If None, config will be used but you can override it here  # 实验名称，为None时将使用配置参数生成
group: ''  # 实验组名称
project: "train_ns"  # W&B项目名称
entity: "dhpitt"  # put your username here  # W&B用户名，请改为您自己的用户名
sweep: False  # 是否进行超参数扫描
log_output: True  # 是否记录输出
eval_interval: 1  # 评估间隔，每隔多少轮进行一次评估