from functools import partialmethod
from typing import Tuple, List, Union, Literal

Number = Union[float, int]  # 定义Number类型为浮点数或整数的联合类型

import torch
import torch.nn as nn
import torch.nn.functional as F

from neuralop.layers.embeddings import GridEmbeddingND, GridEmbedding2D  # 导入网格嵌入层
from neuralop.layers.spectral_convolution import SpectralConv  # 导入频谱卷积层(核心FNO组件)
from neuralop.layers.padding import DomainPadding  # 导入领域填充层
from neuralop.layers.fno_block import FNOBlocks  # 导入FNO块(组合多个FNO层)
from neuralop.layers.channel_mlp import ChannelMLP  # 导入通道MLP层
from neuralop.layers.complex import ComplexValued  # 导入复值支持
from neuralop.models.base_model import BaseModel  # 导入基础模型类


class FNO(BaseModel, name='FNO'):
    """N-Dimensional Fourier Neural Operator. (N维傅里叶神经算子)
    ...省略文档字符串...
    """

    def __init__(
            self,
            n_modes: Tuple[int],  # 每个维度要保留的傅里叶模式数量
            in_channels: int,  # 输入函数的通道数
            out_channels: int,  # 输出函数的通道数
            hidden_channels: int,  # FNO的宽度(即通道数)
            n_layers: int = 4,  # 傅里叶层的数量
            lifting_channel_ratio: int = 2,  # 提升通道与隐藏通道的比例
            projection_channel_ratio: int = 2,  # 投影通道与隐藏通道的比例
            positional_embedding: Union[str, nn.Module] = "grid",  # 位置编码类型
            non_linearity: nn.Module = F.gelu,  # 非线性激活函数
            norm: Literal["ada_in", "group_norm", "instance_norm"] = None,  # 归一化层类型
            complex_data: bool = False,  # 数据是否为复值
            use_channel_mlp: bool = True,  # 是否在每个FNO块后使用MLP层
            channel_mlp_dropout: float = 0,  # 通道MLP的Dropout参数
            channel_mlp_expansion: float = 0.5,  # 通道MLP的扩展系数
            channel_mlp_skip: Literal['linear', 'identity', 'soft-gating'] = "soft-gating",  # 通道MLP的跳跃连接类型
            fno_skip: Literal['linear', 'identity', 'soft-gating'] = "linear",  # FNO层的跳跃连接类型
            resolution_scaling_factor: Union[Number, List[Number]] = None,  # 函数域分辨率的逐层缩放因子
            domain_padding: Union[Number, List[Number]] = None,  # 领域填充百分比
            domain_padding_mode: Literal['symmetric', 'one-sided'] = "symmetric",  # 领域填充模式
            fno_block_precision: str = "full",  # 频谱卷积的精度模式
            stabilizer: str = None,  # 是否使用稳定器
            max_n_modes: Tuple[int] = None,  # 允许增量增加傅里叶域模式数
            factorization: str = None,  # FNO层权重的张量分解
            rank: float = 1.0,  # 上述分解中使用的张量秩
            fixed_rank_modes: bool = False,  # 不分解的模式
            implementation: str = "factorized",  # 实现方式(分解或重构)
            decomposition_kwargs: dict = dict(),  # 张量分解的额外参数
            separable: bool = False,  # 是否使用深度可分离卷积
            preactivation: bool = False,  # 是否使用ResNet风格的预激活
            conv_module: nn.Module = SpectralConv,  # FNO块卷积使用的模块
            **kwargs
    ):

        super().__init__()
        self.n_dim = len(n_modes)  # 从n_modes推断FNO的维度

        # n_modes是一个特殊属性 - 见类属性getter/setter机制
        self._n_modes = n_modes

        # 设置基本属性
        self.hidden_channels = hidden_channels  # 隐藏通道数
        self.in_channels = in_channels  # 输入通道数
        self.out_channels = out_channels  # 输出通道数
        self.n_layers = n_layers  # 层数

        # 使用比例计算提升和投影通道数
        self.lifting_channel_ratio = lifting_channel_ratio
        self.lifting_channels = lifting_channel_ratio * self.hidden_channels

        self.projection_channel_ratio = projection_channel_ratio
        self.projection_channels = projection_channel_ratio * self.hidden_channels

        # 保存其他配置参数
        self.non_linearity = non_linearity  # 非线性激活函数
        self.rank = rank  # 张量分解秩
        self.factorization = factorization  # 分解方法
        self.fixed_rank_modes = fixed_rank_modes  # 固定秩模式
        self.decomposition_kwargs = decomposition_kwargs  # 分解额外参数
        self.fno_skip = (fno_skip,)  # FNO跳跃连接类型
        self.channel_mlp_skip = (channel_mlp_skip,)  # 通道MLP跳跃连接类型
        self.implementation = implementation  # 实现方式
        self.separable = separable  # 是否深度可分离
        self.preactivation = preactivation  # 是否使用预激活
        self.complex_data = complex_data  # 是否使用复数据
        self.fno_block_precision = fno_block_precision  # 块精度

        # 设置位置编码
        if positional_embedding == "grid":  # 如果是默认的"grid"
            spatial_grid_boundaries = [[0., 1.]] * self.n_dim  # 设置网格边界
            self.positional_embedding = GridEmbeddingND(in_channels=self.in_channels,
                                                        dim=self.n_dim,
                                                        grid_boundaries=spatial_grid_boundaries)
        elif isinstance(positional_embedding, GridEmbedding2D):  # 如果是2D网格嵌入
            if self.n_dim == 2:
                self.positional_embedding = positional_embedding
            else:
                raise ValueError(f'Error: expected {self.n_dim}-d positional embeddings, got {positional_embedding}')
        elif isinstance(positional_embedding, GridEmbeddingND):  # 如果是ND网格嵌入
            self.positional_embedding = positional_embedding
        elif positional_embedding == None:  # 如果不使用位置编码
            self.positional_embedding = None
        else:
            raise ValueError(f"Error: tried to instantiate FNO positional embedding with {positional_embedding},\
                              expected one of \'grid\', GridEmbeddingND")

        # 设置领域填充(如果需要)
        if domain_padding is not None and (
                (isinstance(domain_padding, list) and sum(domain_padding) > 0)
                or (isinstance(domain_padding, (float, int)) and domain_padding > 0)
        ):
            self.domain_padding = DomainPadding(
                domain_padding=domain_padding,
                padding_mode=domain_padding_mode,
                resolution_scaling_factor=resolution_scaling_factor,
            )
        else:
            self.domain_padding = None

        self.domain_padding_mode = domain_padding_mode
        self.complex_data = self.complex_data

        # 处理分辨率缩放因子
        if resolution_scaling_factor is not None:
            if isinstance(resolution_scaling_factor, (float, int)):
                resolution_scaling_factor = [resolution_scaling_factor] * self.n_layers
        self.resolution_scaling_factor = resolution_scaling_factor

        # 创建FNO块(核心计算组件)
        self.fno_blocks = FNOBlocks(
            in_channels=hidden_channels,
            out_channels=hidden_channels,
            n_modes=self.n_modes,
            resolution_scaling_factor=resolution_scaling_factor,
            use_channel_mlp=use_channel_mlp,
            channel_mlp_dropout=channel_mlp_dropout,
            channel_mlp_expansion=channel_mlp_expansion,
            non_linearity=non_linearity,
            stabilizer=stabilizer,
            norm=norm,
            preactivation=preactivation,
            fno_skip=fno_skip,
            channel_mlp_skip=channel_mlp_skip,
            complex_data=complex_data,
            max_n_modes=max_n_modes,
            fno_block_precision=fno_block_precision,
            rank=rank,
            fixed_rank_modes=fixed_rank_modes,
            implementation=implementation,
            separable=separable,
            factorization=factorization,
            decomposition_kwargs=decomposition_kwargs,
            conv_module=conv_module,
            n_layers=n_layers,
            **kwargs
        )

        # 创建提升层(从输入维度提升到隐藏维度)
        lifting_in_channels = self.in_channels
        if self.positional_embedding is not None:  # 如果使用位置编码，增加输入通道
            lifting_in_channels += self.n_dim
        # 如果设置了lifting_channels，则创建带隐藏层的MLP
        if self.lifting_channels:
            self.lifting = ChannelMLP(
                in_channels=lifting_in_channels,
                out_channels=self.hidden_channels,
                hidden_channels=self.lifting_channels,
                n_layers=2,
                n_dim=self.n_dim,
                non_linearity=non_linearity
            )
        # 否则创建线性层
        else:
            self.lifting = ChannelMLP(
                in_channels=lifting_in_channels,
                hidden_channels=self.hidden_channels,
                out_channels=self.hidden_channels,
                n_layers=1,
                n_dim=self.n_dim,
                non_linearity=non_linearity
            )
        # 如果使用复数据，转换为复值ChannelMLP
        if self.complex_data:
            self.lifting = ComplexValued(self.lifting)

        # 创建投影层(从隐藏维度降到输出维度)
        self.projection = ChannelMLP(
            in_channels=self.hidden_channels,
            out_channels=out_channels,
            hidden_channels=self.projection_channels,
            n_layers=2,
            n_dim=self.n_dim,
            non_linearity=non_linearity,
        )
        if self.complex_data:
            self.projection = ComplexValued(self.projection)

    def forward(self, x, output_shape=None, **kwargs):
        """FNO的前向传播

        1. 应用可选的位置编码
        2. 通过提升层将输入送入高维潜在空间
        3. 对高维中间函数表示应用可选的域填充
        4. 按顺序应用`n_layers`个傅里叶/FNO层(频谱卷积+跳跃连接+非线性)
        5. 如果应用了域填充，则去除域填充
        6. 将中间函数表示投影到输出通道
        """

        # 处理输出形状参数
        if output_shape is None:
            output_shape = [None] * self.n_layers
        elif isinstance(output_shape, tuple):
            output_shape = [None] * (self.n_layers - 1) + [output_shape]

        # 如果设置了位置编码，则添加
        if self.positional_embedding is not None:
            x = self.positional_embedding(x)

        # 通过提升层
        x = self.lifting(x)

        # 如果需要，应用域填充
        if self.domain_padding is not None:
            x = self.domain_padding.pad(x)

        # 通过所有FNO块
        for layer_idx in range(self.n_layers):
            x = self.fno_blocks(x, layer_idx, output_shape=output_shape[layer_idx])

        # 如果应用了域填充，恢复原始大小
        if self.domain_padding is not None:
            x = self.domain_padding.unpad(x)

        # 通过投影层得到输出
        x = self.projection(x)

        return x

    @property
    def n_modes(self):
        return self._n_modes

    @n_modes.setter
    def n_modes(self, n_modes):
        self.fno_blocks.n_modes = n_modes
        self._n_modes = n_modes


# 以下是针对不同维度的FNO特化类

class FNO1d(FNO):
    """1D傅里叶神经算子
       用于一维问题(如时间序列)
    """

    def __init__(
            self,
            n_modes_height,
            hidden_channels,
            # ... 其他参数与FNO相同，但默认为1D情况设置
    ):
        super().__init__(
            n_modes=(n_modes_height,),  # 注意这是1D，所以只有一个模式维度
            hidden_channels=hidden_channels,
            # ... 传递其他参数
        )
        self.n_modes_height = n_modes_height


class FNO2d(FNO):
    """2D傅里叶神经算子
       用于二维问题(如Navier-Stokes方程)
    """

    def __init__(
            self,
            n_modes_height,
            n_modes_width,
            hidden_channels,
            # ... 其他参数
    ):
        super().__init__(
            n_modes=(n_modes_height, n_modes_width),  # 2D有两个模式维度
            hidden_channels=hidden_channels,
            # ... 传递其他参数
        )
        self.n_modes_height = n_modes_height
        self.n_modes_width = n_modes_width


class FNO3d(FNO):
    """3D傅里叶神经算子
       用于三维问题(如3D流体模拟)
    """

    def __init__(
            self,
            n_modes_height,
            n_modes_width,
            n_modes_depth,
            hidden_channels,
            # ... 其他参数
    ):
        super().__init__(
            n_modes=(n_modes_height, n_modes_width, n_modes_depth),  # 3D有三个模式维度
            hidden_channels=hidden_channels,
            # ... 传递其他参数
        )
        self.n_modes_height = n_modes_height
        self.n_modes_width = n_modes_width
        self.n_modes_depth = n_modes_depth


# 辅助函数，用于创建有不同默认值的新类
def partialclass(new_name, cls, *args, **kwargs):
    """创建具有不同默认值的新类
       这允许我们基于基类创建特化的变种，如TFNO
    """
    __init__ = partialmethod(cls.__init__, *args, **kwargs)
    new_class = type(
        new_name,
        (cls,),
        {
            "__init__": __init__,
            "__doc__": cls.__doc__,
            "forward": cls.forward,
        },
    )
    return new_class


# 使用Tucker分解的FNO变种(TFNO)
TFNO = partialclass("TFNO", FNO, factorization="Tucker")  # 创建使用Tucker分解的FNO
TFNO1d = partialclass("TFNO1d", FNO1d, factorization="Tucker")  # 1D版本
TFNO2d = partialclass("TFNO2d", FNO2d, factorization="Tucker")  # 2D版本(用于Navier-Stokes)
TFNO3d = partialclass("TFNO3d", FNO3d, factorization="Tucker")  # 3D版本