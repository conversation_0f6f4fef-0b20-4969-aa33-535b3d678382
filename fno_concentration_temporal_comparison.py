#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
溶质场时间步预测对比可视化工具 - 基于PyTorch FNO模型
显示同一位置在不同时间步的Ground Truth和Prediction对比
"""

import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
import os
import argparse
import torch
import torch.nn.functional as F
from datetime import datetime
import glob

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 导入FNO模型结构（与train_concentration_field.py中相同）
import torch.nn as nn

class SpectralConv2d(nn.Module):
    def __init__(self, in_channels, out_channels, modes1, modes2):
        super(SpectralConv2d, self).__init__()
        self.in_channels = in_channels
        self.out_channels = out_channels
        # 选择要保留的模式数量
        self.modes1 = modes1  # 第一个维度中的模式数
        self.modes2 = modes2  # 第二个维度中的模式数

        # 缩放系数
        self.scale = 1.0 / (in_channels * out_channels)

        # 创建权重参数 - 复数形式
        self.weights1_real = nn.Parameter(
            self.scale * torch.rand(in_channels, out_channels, self.modes1, self.modes2, dtype=torch.float32)
        )
        self.weights1_imag = nn.Parameter(
            self.scale * torch.rand(in_channels, out_channels, self.modes1, self.modes2, dtype=torch.float32)
        )

    def forward(self, x):
        batchsize = x.shape[0]
        size1 = x.shape[-2]
        size2 = x.shape[-1]

        # 应用FFT, 得到复数格式
        x_ft = torch.fft.rfft2(x)

        # 初始化输出频域张量
        out_ft = torch.zeros(batchsize, self.out_channels, size1, size2 // 2 + 1,
                             device=x.device, dtype=torch.cfloat)

        # 对低频部分进行处理
        weights_complex = torch.complex(self.weights1_real, self.weights1_imag)

        # 限制处理的频域范围
        modes1 = min(self.modes1, size1)
        modes2 = min(self.modes2, size2 // 2 + 1)

        # 复数矩阵乘法
        out_ft[:, :, :modes1, :modes2] = torch.einsum(
            "bixy,ioxy->boxy",
            x_ft[:, :, :modes1, :modes2],
            weights_complex
        )

        # 应用逆FFT，转回空间域
        x = torch.fft.irfft2(out_ft, s=(size1, size2))
        return x

class FNO2d(nn.Module):
    def __init__(self, modes1=16, modes2=16, hidden_channels=48, layers=4, in_channels=1, out_channels=1, dropout=0.1):
        super(FNO2d, self).__init__()
        self.modes1 = modes1
        self.modes2 = modes2
        self.hidden_channels = hidden_channels
        self.layers = layers

        # 输入投影层
        self.fc0 = nn.Linear(in_channels, hidden_channels)

        # FNO层
        self.convs = nn.ModuleList()
        self.ws = nn.ModuleList()
        self.bn_layers = nn.ModuleList()
        self.activations = nn.ModuleList()

        for i in range(layers):
            self.convs.append(SpectralConv2d(hidden_channels, hidden_channels, modes1, modes2))
            self.ws.append(nn.Conv2d(hidden_channels, hidden_channels, 1))
            self.bn_layers.append(nn.BatchNorm2d(hidden_channels))
            self.activations.append(nn.GELU())

        # Dropout层
        self.dropout = nn.Dropout(dropout) if dropout > 0 else None

        # 输出层
        self.fc1 = nn.Linear(hidden_channels, 128)
        self.fc2 = nn.Linear(128, out_channels)

        # 深度残差连接系数
        self.res_scale = nn.Parameter(torch.ones(1))

        # 初始化权重
        self._reset_parameters()

    def _reset_parameters(self):
        """初始化模型参数"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.zeros_(m.bias)
            elif isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.zeros_(m.bias)

    def forward(self, x):
        # 保存输入用于全局残差连接
        identity = x

        # 输入投影: [B, C, H, W] -> [B, H, W, C] -> [B, H, W, hidden_channels] -> [B, hidden_channels, H, W]
        x = x.permute(0, 2, 3, 1)  # [B, H, W, C]
        x = self.fc0(x)  # [B, H, W, hidden_channels]
        x = x.permute(0, 3, 1, 2)  # [B, hidden_channels, H, W]

        x_skip = None

        # FNO层
        for i in range(self.layers):
            # 频谱卷积
            x1 = self.convs[i](x)
            # 空间卷积
            x2 = self.ws[i](x)
            # 组合
            x_combined = x1 + x2
            # 批量归一化
            x_combined = self.bn_layers[i](x_combined)
            # 激活函数
            x_combined = self.activations[i](x_combined)

            # 添加层间残差连接
            if i > 0 and x_skip is not None:
                x_combined = x_combined + self.res_scale * x_skip

            # 应用dropout - 浓度场使用更高的dropout
            if self.dropout is not None and i < self.layers - 1:
                x_combined = self.dropout(x_combined)

            x_skip = x  # 保存当前层输出用于下一层的残差连接
            x = x_combined

        # 输出投影: [B, hidden_channels, H, W] -> [B, H, W, hidden_channels] -> [B, H, W, out_channels] -> [B, out_channels, H, W]
        x = x.permute(0, 2, 3, 1)  # [B, H, W, hidden_channels]
        x = self.fc1(x)
        x = F.gelu(x)
        if self.dropout is not None:
            x = self.dropout(x)
        x = self.fc2(x)
        x = x.permute(0, 3, 1, 2)  # [B, C, H, W]

        # 全局残差连接
        x = x + self.res_scale * identity

        return x

def load_concentration_data_single_step(data_dir, step):
    """加载单个时间步的溶质场数据"""
    file_path = os.path.join(data_dir, f"溶质场第{step}步.csv")
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"文件不存在: {file_path}")
    
    data = pd.read_csv(file_path, header=None, encoding='utf-8').values.astype(np.float32)
    return data

def extract_line_data(data, line_type, line_index):
    """从2D数据中提取指定行或列的数据"""
    if line_type == 'row':
        if line_index >= data.shape[0]:
            raise ValueError(f"行索引 {line_index} 超出数据范围 [0, {data.shape[0]-1}]")
        return data[line_index, :]
    elif line_type == 'col':
        if line_index >= data.shape[1]:
            raise ValueError(f"列索引 {line_index} 超出数据范围 [0, {data.shape[1]-1}]")
        return data[:, line_index]
    else:
        raise ValueError("line_type 必须是 'row' 或 'col'")

def load_fno_model(model_path, device, modes=16, width=48):
    """加载训练好的FNO模型"""
    model = FNO2d(modes1=modes, modes2=modes, hidden_channels=width, layers=4,
                  in_channels=1, out_channels=1, dropout=0.1)
    model.load_state_dict(torch.load(model_path, map_location=device, weights_only=True))
    model.to(device)
    model.eval()
    return model

def predict_with_fno(model, input_data, device, downsample_factor=4):
    """使用FNO模型进行预测"""
    with torch.no_grad():
        # 溶质场归一化（最小-最大归一化）
        data_min, data_max = input_data.min(), input_data.max()
        if data_max > data_min:
            normalized_input = (input_data - data_min) / (data_max - data_min)
        else:
            normalized_input = input_data

        # 降采样输入数据
        if downsample_factor > 1:
            input_tensor = torch.from_numpy(normalized_input).unsqueeze(0).unsqueeze(0).float()
            input_tensor = F.interpolate(input_tensor,
                                       size=(input_data.shape[0]//downsample_factor,
                                            input_data.shape[1]//downsample_factor),
                                       mode='bilinear', align_corners=False)
        else:
            input_tensor = torch.from_numpy(normalized_input).unsqueeze(0).unsqueeze(0).float()

        input_tensor = input_tensor.to(device)

        # 预测
        prediction = model(input_tensor)

        # 上采样回原始分辨率
        if downsample_factor > 1:
            prediction = F.interpolate(prediction,
                                     size=(input_data.shape[0], input_data.shape[1]),
                                     mode='bicubic', align_corners=False)

        # 获取预测结果（已经是归一化的）
        prediction_np = prediction.cpu().numpy()[0, 0]

        # 注意：模型输出的是归一化的预测结果，我们需要直接返回
        # 因为训练时目标数据也是独立归一化的，所以预测结果应该保持在[0,1]范围内
        # 如果需要反归一化到原始范围，需要知道目标数据的归一化参数
        return prediction_np

def parse_time_steps(steps_str):
    """解析时间步字符串"""
    try:
        if ',' in steps_str:
            steps = [int(x.strip()) for x in steps_str.split(',')]
        elif '-' in steps_str:
            parts = steps_str.split('-')
            if len(parts) == 2:
                start, end = int(parts[0]), int(parts[1])
                steps = list(range(start, end + 1, max(1, (end - start) // 10)))
            elif len(parts) == 3:
                start, end, step = int(parts[0]), int(parts[1]), int(parts[2])
                steps = list(range(start, end + 1, step))
            else:
                raise ValueError("范围格式错误")
        else:
            steps = [int(steps_str)]
        return sorted(steps)
    except ValueError:
        raise ValueError("时间步格式错误，请使用逗号分隔或范围格式（如：500,1000,1500 或 500-2000-500）")

def find_available_concentration_model():
    """自动查找可用的溶质场模型文件"""
    model_candidates = [
        './fno_models/con/concentration_field_model.pth',
        './concentration_field_model.pth',
        './models/concentration_field_model.pth'
    ]

    for model_path in model_candidates:
        if os.path.exists(model_path):
            return model_path

    return None

def plot_temporal_gt_pred_comparison(data_dir, model, device, time_steps,
                                   line_type='row', line_index=400, save_path=None, downsample_factor=4):
    """
    绘制不同时间步同一行/列的Ground Truth和Prediction对比
    """
    # 创建美观的图形 - 单个图表显示GT和Prediction对比
    fig, ax = plt.subplots(figsize=(16, 10), facecolor='white')
    ax.set_facecolor('white')

    # 解析时间步
    if isinstance(time_steps, str):
        steps = parse_time_steps(time_steps)
    else:
        steps = time_steps if isinstance(time_steps, list) else [time_steps]

    # 设置颜色
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b', '#e377c2', '#7f7f7f']

    gt_lines_data = []
    pred_lines_data = []
    valid_steps = []

    for i, step in enumerate(steps):
        try:
            print(f"\n🔄 处理第{step}步...")

            # 加载Ground Truth数据
            gt_data = load_concentration_data_single_step(data_dir, step)

            # 使用前一步作为输入进行预测
            if step > 0:
                input_data = load_concentration_data_single_step(data_dir, step - 1)
                pred_data = predict_with_fno(model, input_data, device, downsample_factor)

                if pred_data is None:
                    print(f"⚠️  第{step}步预测失败，跳过")
                    continue
            else:
                print(f"⚠️  第{step}步是起始步，无法预测，跳过")
                continue

            # 提取线数据
            gt_line = extract_line_data(gt_data, line_type, line_index)
            pred_line = extract_line_data(pred_data, line_type, line_index)
            x_coords = np.arange(len(gt_line))

            color = colors[i % len(colors)]
            line_label = f'Step {step}'

            # 在同一图表中绘制Ground Truth和Prediction
            # Ground Truth (实线)
            ax.plot(x_coords, gt_line, color=color, linewidth=2.5,
                   label=f'GT {line_label}', alpha=0.9, linestyle='-')

            # Prediction (虚线)
            ax.plot(x_coords, pred_line, color=color, linewidth=2.5,
                   label=f'Pred {line_label}', alpha=0.9, linestyle='--')

            gt_lines_data.append(gt_line)
            pred_lines_data.append(pred_line)
            valid_steps.append(step)

            print(f"✅ 第{step}步: GT范围[{gt_line.min():.6f}, {gt_line.max():.6f}], "
                  f"Pred范围[{pred_line.min():.6f}, {pred_line.max():.6f}]")

        except Exception as e:
            print(f"⚠️  跳过第{step}步: {str(e)}")
            continue

    if not gt_lines_data:
        print("❌ 没有有效的数据可以绘制")
        return None

    # 设置图形属性
    axis_label = 'X' if line_type == 'row' else 'Y'
    line_label = f'Row {line_index}' if line_type == 'row' else f'Col {line_index}'

    # 设置单个图表样式
    ax.set_xlabel(f'{axis_label} Coordinate', fontsize=12, fontweight='bold')
    ax.set_ylabel('Solute Concentration', fontsize=12, fontweight='bold')
    ax.set_title(f'Concentration Field GT vs Prediction - {line_label} Temporal Evolution',
                fontsize=14, fontweight='bold', pad=15)

    # 优化图例显示 - 将GT和Pred成对显示
    handles, labels = ax.get_legend_handles_labels()
    # 重新排序图例，让同一时间步的GT和Pred相邻显示
    reordered_handles = []
    reordered_labels = []
    n_steps = len(valid_steps)
    for i in range(n_steps):
        # GT在前，Pred在后
        gt_idx = i * 2
        pred_idx = i * 2 + 1
        if gt_idx < len(handles):
            reordered_handles.append(handles[gt_idx])
            reordered_labels.append(labels[gt_idx])
        if pred_idx < len(handles):
            reordered_handles.append(handles[pred_idx])
            reordered_labels.append(labels[pred_idx])

    ax.legend(reordered_handles, reordered_labels, fontsize=10, frameon=True,
             fancybox=True, shadow=True, loc='upper right',
             bbox_to_anchor=(0.98, 0.98), ncol=2)
    ax.grid(True, alpha=0.3, linestyle='-', linewidth=0.5)
    ax.set_axisbelow(True)

    # 设置坐标轴样式
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)
    ax.spines['left'].set_linewidth(0.8)
    ax.spines['bottom'].set_linewidth(0.8)

    # 计算统计信息
    all_gt_data = np.concatenate(gt_lines_data)
    all_pred_data = np.concatenate(pred_lines_data)

    # 计算误差统计
    errors = []
    for gt_line, pred_line in zip(gt_lines_data, pred_lines_data):
        error = np.mean(np.abs(gt_line - pred_line))
        errors.append(error)

    mean_error = np.mean(errors)
    max_error = np.max(errors)

    # 添加统计信息框
    stats_text = f'Time Steps: {len(valid_steps)}\n' \
                f'Step Range: {valid_steps[0]} → {valid_steps[-1]}\n' \
                f'Mean MAE: {mean_error:.8f}\n' \
                f'Max MAE: {max_error:.8f}\n' \
                f'GT Range: [{all_gt_data.min():.6f}, {all_gt_data.max():.6f}]\n' \
                f'Pred Range: [{all_pred_data.min():.6f}, {all_pred_data.max():.6f}]'

    ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, fontsize=10,
           verticalalignment='top', bbox=dict(boxstyle='round,pad=0.5',
           facecolor='lightcyan', alpha=0.8, edgecolor='teal', linewidth=1))

    plt.tight_layout()

    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        print(f"📊 图表已保存到: {save_path}")

    plt.show()
    return gt_lines_data, pred_lines_data, valid_steps

def interactive_concentration_temporal_console():
    """交互式溶质场时间步预测对比控制台"""
    print("🎯 " + "="*60)
    print("    溶质场时间步预测对比可视化工具 (PyTorch FNO)")
    print("="*64)

    # 获取数据目录
    default_data_dir = './ConcentrationField_Data/'
    data_dir = input(f"📂 请输入溶质场数据目录路径 (默认: {default_data_dir}): ").strip()
    if not data_dir:
        data_dir = default_data_dir

    if not os.path.exists(data_dir):
        print(f"❌ 目录不存在: {data_dir}")
        return None

    # 获取模型路径
    default_model = find_available_concentration_model()
    if default_model:
        print(f"🤖 找到可用模型: {default_model}")
        model_input = input(f"请输入模型路径 (默认: {default_model}): ").strip()
        model_path = model_input if model_input else default_model
    else:
        model_path = input("🤖 请输入溶质场模型路径: ").strip()
        if not model_path:
            print("❌ 必须提供模型路径")
            return None

    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        return None

    # 选择对比类型
    print("\n📏 选择对比类型:")
    print("  1. Row (行对比)")
    print("  2. Column (列对比)")

    while True:
        type_choice = input("请选择类型 (1/2, 默认1): ").strip()
        if type_choice == '2':
            line_type = 'col'
            break
        elif type_choice == '1' or type_choice == '':
            line_type = 'row'
            break
        else:
            print("❌ 请输入 1 或 2")

    # 获取行/列索引
    while True:
        index_input = input(f"📍 请输入要分析的{line_type}索引 (默认: 400): ").strip()
        if not index_input:
            line_index = 400
            break
        try:
            line_index = int(index_input)
            if line_index < 0:
                print("❌ 索引必须为非负整数")
                continue
            break
        except ValueError:
            print("❌ 请输入有效的整数")

    # 获取时间步
    print("\n⏰ 时间步设置:")
    print("  支持格式: 单个数字(如: 2000), 逗号分隔(如: 500,1000,1500,2000), 范围(如: 500-2000-500)")

    time_steps_input = input("请输入时间步 (默认: 500,1000,1500,2000): ").strip()
    if not time_steps_input:
        time_steps = "500,1000,1500,2000"
    else:
        time_steps = time_steps_input

    # 获取降采样因子
    while True:
        downsample_input = input("🔍 请输入降采样因子 (默认: 4): ").strip()
        if not downsample_input:
            downsample_factor = 4
            break
        try:
            downsample_factor = int(downsample_input)
            if downsample_factor < 1:
                print("❌ 降采样因子必须大于等于1")
                continue
            break
        except ValueError:
            print("❌ 请输入有效的整数")

    # 设置保存路径
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_dir = r'E:\1neuraloperator-main\fno_models\con-line'
    os.makedirs(results_dir, exist_ok=True)
    save_path = os.path.join(results_dir,
                            f'concentration_temporal_pred_{line_type}_{line_index}_{timestamp}.png')

    print("\n" + "="*64)
    print("📋 配置摘要:")
    print(f"   数据目录: {data_dir}")
    print(f"   模型路径: {model_path}")
    print(f"   对比类型: {line_type}")
    print(f"   索引: {line_index}")
    print(f"   时间步: {time_steps}")
    print(f"   降采样因子: {downsample_factor}")
    print(f"   保存路径: {save_path}")
    print("="*64)

    return {
        'data_dir': data_dir,
        'model_path': model_path,
        'line_type': line_type,
        'line_index': line_index,
        'time_steps': time_steps,
        'downsample_factor': downsample_factor,
        'save_path': save_path
    }

def main():
    parser = argparse.ArgumentParser(description='溶质场时间步预测对比可视化工具 (PyTorch FNO)')
    parser.add_argument('-i', '--interactive', action='store_true',
                       help='启用交互式模式')
    parser.add_argument('--data_dir', type=str, default='./ConcentrationField_Data/',
                       help='溶质场数据目录路径')
    parser.add_argument('--model_path', type=str, default=None,
                       help='FNO模型路径')
    parser.add_argument('--line_type', type=str, choices=['row', 'col'], default='row',
                       help='对比类型: row 或 col')
    parser.add_argument('--line_index', type=int, default=100,
                       help='要分析的行/列索引')
    parser.add_argument('--time_steps', type=str, default='500,1000,2000,3000',
                       help='时间步，支持逗号分隔或范围格式')
    parser.add_argument('--downsample_factor', type=int, default=4,
                       help='降采样因子，应与训练时保持一致')
    parser.add_argument('--modes', type=int, default=16,
                       help='FNO模型的modes参数')
    parser.add_argument('--width', type=int, default=48,
                       help='FNO模型的width参数')
    parser.add_argument('--save_path', type=str, default=r'E:\1neuraloperator-main\fno_models\con-line\duo.png',
                       help='图片保存路径')

    args = parser.parse_args()

    # 交互式模式
    if args.interactive:
        config = interactive_concentration_temporal_console()
        if config is None:
            return

        args.data_dir = config['data_dir']
        args.model_path = config['model_path']
        args.line_type = config['line_type']
        args.line_index = config['line_index']
        args.time_steps = config['time_steps']
        args.downsample_factor = config['downsample_factor']
        args.save_path = config['save_path']

    # 自动查找模型
    if args.model_path is None:
        args.model_path = find_available_concentration_model()
        if args.model_path is None:
            print("❌ 未找到可用的溶质场模型文件")
            print("请确保以下位置存在模型文件:")
            print("  - ./fno_models/con/concentration_field_model.pth")
            print("  - ./concentration_field_model.pth")
            print("  - ./models/concentration_field_model.pth")
            return

    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🔧 使用设备: {device}")

    # 加载模型
    try:
        print(f"📂 加载模型: {args.model_path}")
        model = load_fno_model(args.model_path, device, args.modes, args.width)
        print("✅ 模型加载成功")
    except Exception as e:
        print(f"❌ 模型加载失败: {str(e)}")
        return

    # 设置保存路径
    if args.save_path is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_dir = r'E:\1neuraloperator-main\fno_models\con-line'
        os.makedirs(results_dir, exist_ok=True)
        args.save_path = os.path.join(results_dir,
                                    f'concentration_temporal_pred_{args.line_type}_{args.line_index}_{timestamp}.png')

    # 绘制时间步预测对比图
    try:
        result = plot_temporal_gt_pred_comparison(
            args.data_dir, model, device, args.time_steps,
            args.line_type, args.line_index, args.save_path, args.downsample_factor
        )

        if result:
            gt_lines_data, pred_lines_data, valid_steps = result
            print(f"\n📊 成功绘制 {len(valid_steps)} 个时间步的GT vs Prediction对比图")

            # 计算总体统计
            all_gt = np.concatenate(gt_lines_data)
            all_pred = np.concatenate(pred_lines_data)
            overall_mae = np.mean(np.abs(all_gt - all_pred))

            print(f"📈 总体统计:")
            print(f"   时间步范围: {valid_steps[0]} → {valid_steps[-1]}")
            print(f"   总体MAE: {overall_mae:.8f}")
            print(f"   GT溶质浓度范围: [{all_gt.min():.6f}, {all_gt.max():.6f}]")
            print(f"   Pred溶质浓度范围: [{all_pred.min():.6f}, {all_pred.max():.6f}]")

    except Exception as e:
        print(f"❌ 绘制时间步预测对比图时出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
