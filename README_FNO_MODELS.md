# 傅里叶神经算子(FNO)物理场预测模型使用指南

本项目包含三个用于物理场预测的傅里叶神经算子(FNO)模型：相场、温度场和浓度场。这些模型使用先进的神经网络技术从当前物理场状态预测未来状态。

## 模型特点

- **傅里叶层**: 利用傅里叶变换捕捉物理场的多尺度特征
- **层间残差连接**: 提高梯度流动，实现更深层网络训练
- **批归一化**: 加速训练过程并提高模型稳定性
- **场特化设计**: 每个场采用专门的损失函数和网络结构

## 快速开始

### 环境要求

- Python 3.8+
- PyTorch 1.9+
- NumPy, Matplotlib, tqdm, PIL

### 训练相场模型

```bash
python train_phase_field.py --data_dir ./PhaseField_Data --mode train --modes 16 --width 48
```

### 训练温度场模型

```bash
python train_temperature_field.py --data_dir ./TemperatureField_Data --mode train --modes 14 --width 32
```

### 训练浓度场模型

```bash
python train_concentration_field.py --data_dir ./ConcentrationField_Data --mode train --modes 16 --width 48
```

## 运行模式

所有三个模型脚本都支持以下运行模式，可通过`--mode`参数指定：

- **train**: 从头训练新模型（默认模式）
- **predict**: 仅使用已训练模型进行预测，不进行训练
- **retrain**: 加载已有模型并继续训练

例如，加载已有相场模型进行预测：

```bash
python train_phase_field.py --data_dir ./PhaseField_Data --mode predict
```

继续训练已有温度场模型：

```bash
python train_temperature_field.py --data_dir ./TemperatureField_Data --mode retrain --num_epochs 100
```

## 常见问题解决

### 模型大小不匹配错误

如果遇到模型大小不匹配错误（如当前模型使用modes=16,width=48，但保存的模型使用modes=12,width=32），脚本会自动检测并调整为已保存模型的大小。

如需强制使用命令行指定的模型大小，可添加`--force_model_size`参数：

```bash
python train_phase_field.py --mode predict --modes 16 --width 48 --force_model_size
```

### 找不到模型文件

当指定`predict`或`retrain`模式但找不到对应模型文件时，脚本将自动切换到`train`模式。确保模型文件在正确位置：

- 相场模型: `./fno_models/phi/phase_field_model.pth`
- 温度场模型: `./fno_models/temp/temp_model.pth`
- 浓度场模型: `./fno_models/con/solute_model.pth`

可以通过`--save_dir`参数修改模型保存路径。

## 命令行参数说明

所有三个脚本支持以下共同参数：

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `--mode` | 运行模式：train, predict, retrain | train |
| `--data_dir` | 数据目录路径 | ./XXXField_Data |
| `--save_dir` | 模型保存目录 | ./fno_models/xxx |
| `--modes` | 傅里叶模式数量 | 相场/浓度场:16, 温度场:14 |
| `--width` | 网络宽度 | 相场/浓度场:48, 温度场:32 |
| `--num_epochs` | 训练轮数 | 相场/温度场:360, 浓度场:5 |
| `--batch_size` | 批处理大小 | 4 |
| `--learning_rate` | 学习率 | 0.001 |
| `--sample_stride` | 样本帧间步长 | 5 |
| `--downsample_factor` | 空间降采样因子 | 4 |
| `--force_model_size` | 强制使用命令行指定的模型尺寸 | False |

## 模型架构

各个场的FNO模型在结构上略有差异，以适应不同物理场的特性：

- **相场模型**: 优化用于清晰的相界面，更大的网络(modes=16, width=48)，使用边界感知损失函数
- **温度场模型**: 针对温度梯度优化，使用自适应加权损失函数，中等大小网络(modes=14, width=32)
- **浓度场模型**: 处理溶质扩散，使用大型网络(modes=16, width=48)和较高dropout率(0.1)，优化大范围空间依赖

## 参考资料

- [Neural Operator论文](https://arxiv.org/abs/2010.08895)
- [FNO GitHub实现](https://github.com/zongyi-li/fourier_neural_operator) 