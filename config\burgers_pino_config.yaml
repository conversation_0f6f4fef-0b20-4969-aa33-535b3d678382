default: &DEFAULT

  #General
  # For computing compression
  n_params_baseline: None
  verbose: True
  arch: 'tfno2d'

  #Distributed computing
  distributed:
    use_distributed: False
    wireup_info: 'mpi'
    wireup_store: 'tcp'
    model_parallel_size: 2
    seed: 666

  # FNO related
  tfno2d:
    data_channels: 3
    n_modes_height: 15
    n_modes_width: 15
    hidden_channels: 24
    lifting_channel_ratio: 1
    projection_channel_ratio: 1
    n_layers: 5
    domain_padding: None
    domain_padding_mode: 'one-sided'
    fft_norm: 'forward'
    norm: 'group_norm'
    skip: 'linear'
    implementation: 'factorized'
    separable: 0
    preactivation: 0
    half_prec_fourier: False
    half_prec_inverse: False
    stabilizer: None

    use_channel_mlp: 1
    channel_mlp_expansion: 0.5
    channel_mlp_dropout: 0

    factorization: None
    rank: 0.05
    fixed_rank_modes: None
    dropout: 0.0
    tensor_lasso_penalty: 0.0
    joint_factorization: False

  # Optimizer
  opt:
    n_epochs: 10000
    learning_rate: 0.0001
    training_loss: ['equation', 'ic']
    pino_method: 'fdm'
    loss_weights:
      'l2': 0.0
      'equation': .2
      'ic': .8
    weight_decay: 1e-4
    amp_autocast: False

    scheduler_T_max: 500 # For cosine only, typically take n_epochs
    scheduler_patience: 100 # For ReduceLROnPlateau only
    scheduler: 'ReduceLROnPlateau' # Or 'CosineAnnealingLR' OR 'ReduceLROnPlateau'
    step_size: 60
    gamma: 0.5
    precision_schedule: None

  # Dataset related
  data:
    folder: '/home/<USER>/data/burgers/burgers.npz' 
    batch_size: 16
    n_train: 800
    test_batch_sizes: [16]
    n_tests: [400]
    spatial_length: 128
    temporal_length: 101

    encode_input: False
    encode_output: False
    include_endpoint: [True, False]

  # Patching
  patching:
    levels: 0
    padding: 0
    stitching: False

  # Weights and biases
  wandb:
    log: False
    name: None # If None, config will be used but you can override it here
    group: ""
    project: ""
    entity: "" # put your username here
    sweep: False
    log_output: True
    eval_interval: 1