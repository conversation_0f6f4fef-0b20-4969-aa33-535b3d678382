#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量修复FNO可视化工具中的模型结构
"""

import os
import re

def fix_spectral_conv2d(content):
    """修复SpectralConv2d类定义"""
    # 查找SpectralConv2d类的开始和结束
    pattern = r'class SpectralConv2d\(nn\.Module\):.*?(?=class|\Z)'
    
    new_spectral_conv = '''class SpectralConv2d(nn.Module):
    def __init__(self, in_channels, out_channels, modes1, modes2):
        super(SpectralConv2d, self).__init__()
        self.in_channels = in_channels
        self.out_channels = out_channels
        # 选择要保留的模式数量
        self.modes1 = modes1  # 第一个维度中的模式数
        self.modes2 = modes2  # 第二个维度中的模式数

        # 缩放系数
        self.scale = 1.0 / (in_channels * out_channels)

        # 创建权重参数 - 复数形式
        self.weights1_real = nn.Parameter(
            self.scale * torch.rand(in_channels, out_channels, self.modes1, self.modes2, dtype=torch.float32)
        )
        self.weights1_imag = nn.Parameter(
            self.scale * torch.rand(in_channels, out_channels, self.modes1, self.modes2, dtype=torch.float32)
        )

    def forward(self, x):
        batchsize = x.shape[0]
        size1 = x.shape[-2]
        size2 = x.shape[-1]

        # 应用FFT, 得到复数格式
        x_ft = torch.fft.rfft2(x)

        # 初始化输出频域张量
        out_ft = torch.zeros(batchsize, self.out_channels, size1, size2 // 2 + 1, 
                             device=x.device, dtype=torch.cfloat)
        
        # 对低频部分进行处理
        weights_complex = torch.complex(self.weights1_real, self.weights1_imag)
        
        # 限制处理的频域范围
        modes1 = min(self.modes1, size1)
        modes2 = min(self.modes2, size2 // 2 + 1)
        
        # 复数矩阵乘法
        out_ft[:, :, :modes1, :modes2] = torch.einsum(
            "bixy,ioxy->boxy", 
            x_ft[:, :, :modes1, :modes2], 
            weights_complex
        )

        # 应用逆FFT，转回空间域
        x = torch.fft.irfft2(out_ft, s=(size1, size2))
        return x

'''
    
    return re.sub(pattern, new_spectral_conv, content, flags=re.DOTALL)

def add_res_scale_to_fno2d(content):
    """为FNO2d类添加res_scale参数"""
    # 查找FNO2d的__init__方法中的输出层部分
    pattern = r'(self\.fc1 = nn\.Linear\(hidden_channels, 128\)\s+self\.fc2 = nn\.Linear\(128, out_channels\))\s+(self\.dropout_layer = nn\.Dropout\(dropout\))'
    
    replacement = r'\1\n        \n        # 深度残差连接系数\n        self.res_scale = nn.Parameter(torch.ones(1))\n        \n        # 初始化权重\n        self._reset_parameters()\n    \n    def _reset_parameters(self):\n        """初始化模型参数"""\n        for m in self.modules():\n            if isinstance(m, nn.Linear):\n                nn.init.xavier_uniform_(m.weight)\n                if m.bias is not None:\n                    nn.init.zeros_(m.bias)\n            elif isinstance(m, nn.Conv2d):\n                nn.init.kaiming_normal_(m.weight, mode=\'fan_out\', nonlinearity=\'relu\')\n                if m.bias is not None:\n                    nn.init.zeros_(m.bias)\n\n        \2'
    
    content = re.sub(pattern, replacement, content)
    
    # 修复forward方法
    # 查找forward方法的开始
    forward_pattern = r'def forward\(self, x\):(.*?)return x'
    
    new_forward = '''def forward(self, x):
        # 保存输入用于全局残差连接
        identity = x
        
        # 输入投影: [B, C, H, W] -> [B, H, W, C] -> [B, H, W, hidden_channels] -> [B, hidden_channels, H, W]
        x = x.permute(0, 2, 3, 1)  # [B, H, W, C]
        x = self.fc0(x)  # [B, H, W, hidden_channels]
        x = x.permute(0, 3, 1, 2)  # [B, hidden_channels, H, W]
        
        x_skip = None
        
        # FNO层
        for i in range(self.layers):
            # 频谱卷积
            x1 = self.convs[i](x)
            # 空间卷积
            x2 = self.ws[i](x)
            # 组合
            x_combined = x1 + x2
            # 批量归一化
            x_combined = self.bn_layers[i](x_combined)
            # 激活函数
            x_combined = self.activations[i](x_combined)
            
            # 添加层间残差连接
            if i > 0 and x_skip is not None:
                x_combined = x_combined + self.res_scale * x_skip
            
            # 应用dropout
            if hasattr(self, 'dropout') and self.dropout is not None and i < self.layers - 1:
                x_combined = self.dropout(x_combined)
            elif hasattr(self, 'dropout_layer') and i < self.layers - 1:
                x_combined = self.dropout_layer(x_combined)
                
            x_skip = x  # 保存当前层输出用于下一层的残差连接
            x = x_combined
        
        # 输出投影: [B, hidden_channels, H, W] -> [B, H, W, hidden_channels] -> [B, H, W, out_channels] -> [B, out_channels, H, W]
        x = x.permute(0, 2, 3, 1)  # [B, H, W, hidden_channels]
        x = self.fc1(x)
        x = F.gelu(x)
        if hasattr(self, 'dropout') and self.dropout is not None:
            x = self.dropout(x)
        elif hasattr(self, 'dropout_layer'):
            x = self.dropout_layer(x)
        x = self.fc2(x)
        x = x.permute(0, 3, 1, 2)  # [B, C, H, W]
        
        # 全局残差连接
        x = x + self.res_scale * identity
        
        return x'''
    
    return re.sub(forward_pattern, new_forward, content, flags=re.DOTALL)

def fix_torch_load(content):
    """修复torch.load调用"""
    pattern = r'torch\.load\(([^,]+),\s*map_location=([^)]+)\)'
    replacement = r'torch.load(\1, map_location=\2, weights_only=True)'
    return re.sub(pattern, replacement, content)

def fix_file(filepath):
    """修复单个文件"""
    print(f"修复文件: {filepath}")
    
    with open(filepath, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 应用修复
    content = fix_spectral_conv2d(content)
    content = add_res_scale_to_fno2d(content)
    content = fix_torch_load(content)
    
    with open(filepath, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"✅ 完成修复: {filepath}")

def main():
    """主函数"""
    files_to_fix = [
        'fno_phase_multi_line_comparison.py',
        'fno_phase_temporal_comparison.py', 
        'fno_temperature_multi_line_comparison.py',
        'fno_temperature_temporal_comparison.py'
    ]
    
    for filepath in files_to_fix:
        if os.path.exists(filepath):
            fix_file(filepath)
        else:
            print(f"⚠️  文件不存在: {filepath}")

if __name__ == '__main__':
    main()
