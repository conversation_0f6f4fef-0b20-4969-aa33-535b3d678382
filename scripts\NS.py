import sys  # 导入系统模块，用于系统级功能和标准输出流控制

from configmypy import ConfigPipeline, YamlConfig, ArgparseConfig  # 导入配置管理工具
from pathlib import Path  # 导入路径处理工具
import torch  # 导入PyTorch深度学习框架
from torch.utils.data import DataLoader, DistributedSampler  # 导入数据加载器和分布式采样器
import torch.distributed as dist  # 导入分布式训练工具
import wandb  # 导入权重与偏差(Weights & Biases)实验跟踪工具

from neuralop import H1Loss, LpLoss, Trainer, get_model  # 导入神经算子相关的损失函数、训练器和模型获取函数
from neuralop.data.datasets.navier_stokes import load_navier_stokes_pt  # 导入Navier-Stokes数据集加载函数
from neuralop.data.transforms.data_processors import MGPatchingDataProcessor  # 导入多网格补丁数据处理器
from neuralop.utils import get_wandb_api_key, count_model_params  # 导入工具函数
from neuralop.mpu.comm import get_local_rank  # 导入分布式通信中获取本地等级的函数
from neuralop.training import setup, AdamW  # 导入训练设置和优化器

print(f"PyTorch是否检测到CUDA: {torch.cuda.is_available()}")  # 打印是否检测到CUDA环境
print(f"可用GPU数量: {torch.cuda.device_count()}")  # 打印可用的GPU数量
if torch.cuda.is_available():
    print(f"当前GPU: {torch.cuda.get_device_name()}")  # 如果有GPU，打印GPU名称
# Read the configuration
config_name = "default"  # 设置默认配置名称
pipe = ConfigPipeline(  # 创建配置管道
    [
        YamlConfig(  # 从YAML文件加载配置
            "./navier_stokes_config.yaml", config_name="default", config_folder="../config"
        ),
        ArgparseConfig(infer_types=True, config_name=None, config_file=None),  # 从命令行参数加载配置
        YamlConfig(config_folder="../config"),  # 从配置文件夹加载额外配置
    ]
)
config = pipe.read_conf()  # 读取配置
config_name = pipe.steps[-1].config_name  # 获取最后一步的配置名称

# Set-up distributed communication, if using
device, is_logger = setup(config)  # 设置设备和日志记录器

# 添加GPU使用确认信息
print(f"选择的设备: {device}")  # 打印选择的设备
if device.type == 'cuda':
    print(f"当前使用的GPU: {torch.cuda.get_device_name(device.index)}")  # 打印当前使用的GPU名称
    print(f"GPU内存使用情况: {torch.cuda.memory_allocated(device.index)/1024**2:.2f} MB")  # 打印GPU内存使用情况
    print(f"GPU内存缓存: {torch.cuda.memory_reserved(device.index)/1024**2:.2f} MB")  # 打印GPU内存缓存
else:
    print("警告: 使用的是CPU，训练速度会很慢!")  # 如果使用CPU，给出警告

# Set up WandB logging
wandb_init_args = None  # 初始化Weights & Biases参数为空
if config.wandb.log and is_logger:  # 如果启用了WandB日志记录且当前进程是日志记录器
    print(config.wandb.log)  # 打印WandB日志设置
    print(config)  # 打印整个配置
    wandb.login(key=get_wandb_api_key())  # 登录WandB
    if config.wandb.name:  # 如果配置中指定了实验名称
        wandb_name = config.wandb.name  # 使用配置中的名称
    else:  # 否则根据配置参数构建名称
        wandb_name = "_".join(
            f"{var}"
            for var in [
                config_name,  # 配置名称
                config.fno.n_layers,  # FNO层数
                config.fno.n_modes,  # FNO模式数
                config.fno.hidden_channels,  # 隐藏通道数
                config.fno.factorization,  # 因子分解方法
                config.fno.rank,  # 秩设置
                config.patching.levels,  # 分层级别
                config.patching.padding,  # 填充大小
            ]
        )
    wandb_init_args = dict(  # 构建WandB初始化参数
        config=config,  # 配置
        name=wandb_name,  # 实验名称
        group=config.wandb.group,  # 实验组
        project=config.wandb.project,  # 项目名称
        entity=config.wandb.entity,  # 实体（用户或组织）
    )
    if config.wandb.sweep:  # 如果进行超参数扫描
        for key in wandb.config.keys():  # 遍历WandB配置键
            config.params[key] = wandb.config[key]  # 更新配置参数
    wandb.init(**wandb_init_args)  # 初始化WandB

# Make sure we only print information when needed
config.verbose = config.verbose and is_logger  # 只在日志记录器进程中启用详细输出

# Print config to screen
if config.verbose:  # 如果启用了详细输出
    pipe.log()  # 打印配置信息
    sys.stdout.flush()  # 刷新标准输出流

data_dir = Path(f"~/{config.data.folder}").expanduser()  # 获取数据目录的完整路径

# Loading the Navier-Stokes dataset in 128x128 resolution
train_loader, test_loaders, data_processor = load_navier_stokes_pt(  # 加载Navier-Stokes数据集
    data_root=data_dir,  # 数据根目录
    train_resolution=config.data.train_resolution,  # 训练数据分辨率
    n_train=config.data.n_train,  # 训练样本数量
    batch_size=config.data.batch_size,  # 批量大小
    test_resolutions=config.data.test_resolutions,  # 测试数据分辨率
    n_tests=config.data.n_tests,  # 测试样本数量
    test_batch_sizes=config.data.test_batch_sizes,  # 测试批量大小
    encode_input=config.data.encode_input,  # 是否对输入进行编码
    encode_output=config.data.encode_output,  # 是否对输出进行编码
)

model = get_model(config)  # 根据配置创建模型
model = model.to(device)  # 将模型移到指定设备(CPU或GPU)
# convert dataprocessor to an MGPatchingDataprocessor if patching levels > 0
if config.patching.levels > 0:  # 如果启用了多网格分层
    data_processor = MGPatchingDataProcessor(model=model,  # 创建多网格补丁数据处理器
                                             in_normalizer=data_processor.in_normalizer,  # 输入归一化器
                                             out_normalizer=data_processor.out_normalizer,  # 输出归一化器
                                             padding_fraction=config.patching.padding,  # 填充比例
                                             stitching=config.patching.stitching,  # 是否拼接
                                             levels=config.patching.levels,  # 分层级别
                                             use_distributed=config.distributed.use_distributed)  # 是否使用分布式
data_processor = data_processor.to(device)  # 将数据处理器移到指定设备

# Use distributed data parallel

# Reconfigure DataLoaders to use a DistributedSampler
# if in distributed data parallel mode
if config.distributed.use_distributed:  # 如果使用分布式训练
    train_db = train_loader.dataset  # 获取训练数据集
    train_sampler = DistributedSampler(train_db, rank=get_local_rank())  # 创建分布式采样器
    train_loader = DataLoader(dataset=train_db,  # 重新配置训练数据加载器
                              batch_size=config.data.batch_size,  # 批量大小
                              sampler=train_sampler)  # 使用分布式采样器
    for (res, loader), batch_size in zip(test_loaders.items(), config.data.test_batch_sizes):  # 遍历测试加载器

        test_db = loader.dataset  # 获取测试数据集
        test_sampler = DistributedSampler(test_db, rank=get_local_rank())  # 创建测试分布式采样器
        test_loaders[res] = DataLoader(dataset=test_db,  # 重新配置测试数据加载器
                              batch_size=batch_size,  # 批量大小
                              shuffle=False,  # 不打乱数据
                              sampler=test_sampler)  # 使用分布式采样器

# Create the optimizer
optimizer = AdamW(  # 创建AdamW优化器
    model.parameters(),  # 优化模型参数
    lr=config.opt.learning_rate,  # 学习率
    weight_decay=config.opt.weight_decay,  # 权重衰减
)

if config.opt.scheduler == "ReduceLROnPlateau":  # 如果使用按验证集性能调整学习率的调度器
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
        optimizer,  # 优化器
        factor=config.opt.gamma,  # 学习率衰减因子
        patience=config.opt.scheduler_patience,  # 容忍的训练轮数
        mode="min",  # 监控模式，目标是最小化损失
    )
elif config.opt.scheduler == "CosineAnnealingLR":  # 如果使用余弦退火学习率调度器
    scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
        optimizer, T_max=config.opt.scheduler_T_max  # 最大轮数
    )
elif config.opt.scheduler == "StepLR":  # 如果使用阶梯式学习率调度器
    scheduler = torch.optim.lr_scheduler.StepLR(
        optimizer, step_size=config.opt.step_size, gamma=config.opt.gamma  # 步长和衰减因子
    )
else:
    raise ValueError(f"Got scheduler={config.opt.scheduler}")  # 如果调度器类型无效，抛出错误


# Creating the losses
l2loss = LpLoss(d=2, p=2)  # 创建L2损失（L2范数损失）
h1loss = H1Loss(d=2)  # 创建H1损失（Sobolev范数损失）
if config.opt.training_loss == "l2":  # 如果训练损失是L2
    train_loss = l2loss  # 使用L2损失训练
elif config.opt.training_loss == "h1":  # 如果训练损失是H1
    train_loss = h1loss  # 使用H1损失训练
else:
    raise ValueError(  # 如果损失类型无效，抛出错误
        f'Got training_loss={config.opt.training_loss} '
        f'but expected one of ["l2", "h1"]'
    )
eval_losses = {"h1": h1loss, "l2": l2loss}  # 设置评估损失

if config.verbose:  # 如果启用了详细输出
    print("\n### MODEL ###\n", model)  # 打印模型结构
    print("\n### OPTIMIZER ###\n", optimizer)  # 打印优化器信息
    print("\n### SCHEDULER ###\n", scheduler)  # 打印学习率调度器信息
    print("\n### LOSSES ###")  # 打印损失函数信息
    print(f"\n * Train: {train_loss}")  # 打印训练损失
    print(f"\n * Test: {eval_losses}")  # 打印测试损失
    print(f"\n### Beginning Training...\n")  # 打印训练开始信息
    print(f"当前使用设备: {device}")  # 打印当前使用的设备
    sys.stdout.flush()  # 刷新标准输出流


trainer = Trainer(  # 创建训练器
    model=model,  # 模型
    n_epochs=config.opt.n_epochs,  # 训练轮数
    data_processor=data_processor,  # 数据处理器
    device=device,  # 设备
    mixed_precision=config.opt.amp_autocast,  # 是否使用混合精度训练
    eval_interval=config.wandb.eval_interval,  # 评估间隔
    log_output=config.wandb.log_output,  # 是否记录输出
    use_distributed=config.distributed.use_distributed,  # 是否使用分布式
    verbose=config.verbose,  # 是否显示详细信息
    wandb_log = config.wandb.log  # 是否使用WandB记录
)

# Log parameter count
if is_logger:  # 如果当前进程是日志记录器
    n_params = count_model_params(model)  # 计算模型参数数量

    if config.verbose:  # 如果启用详细输出
        print(f"\nn_params: {n_params}")  # 打印参数数量
        sys.stdout.flush()  # 刷新标准输出流

    if config.wandb.log:  # 如果启用WandB日志
        to_log = {"n_params": n_params}  # 准备记录参数数量
        if config.n_params_baseline is not None:  # 如果有基准参数数量
            to_log["n_params_baseline"] = (config.n_params_baseline,)  # 记录基准数量
            to_log["compression_ratio"] = (config.n_params_baseline / n_params,)  # 计算压缩比
            to_log["space_savings"] = 1 - (n_params / config.n_params_baseline)  # 计算节省空间
        wandb.log(to_log, commit=False)  # 记录到WandB
        wandb.watch(model)  # 监控模型参数和梯度


trainer.train(  # 开始训练过程
    train_loader,  # 训练数据加载器
    test_loaders,  # 测试数据加载器
    optimizer,  # 优化器
    scheduler,  # 学习率调度器
    regularizer=False,  # 不使用正则化器
    training_loss=train_loss,  # 训练损失函数
    eval_losses=eval_losses,  # 评估损失函数
)

if config.wandb.log and is_logger:  # 如果启用WandB日志且当前进程是日志记录器
    wandb.finish()  # 完成WandB会话

if dist.is_initialized():  # 如果初始化了分布式环境
    dist.destroy_process_group()  # 销毁分布式进程组
