name: Build documentation
# Action adapted from https://github.com/tensorly/tensorly

on: [push, pull_request]

jobs:
  build:

    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v3
    - name: Install Python
      uses: actions/setup-python@v4
      with:
        python-version: 3.13
    # - name: Install TensorLy and TensorLy-Torch dev
    #   run: |
    #     CWD=`pwd`
    #     echo 'Cloning repos in ${CWD}'
    #     mkdir git_repos
    #     cd git_repos
    #     git clone https://github.com/tensorly/tensorly
    #     cd tensorly
    #     python -m pip install -e .
    #     cd ..
    #     git clone https://github.com/tensorly/torch
    #     cd torch
    #     python -m pip install -e .
    #     cd ../..
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        python -m pip install setuptools
        python -m pip install torch torchvision --extra-index-url https://download.pytorch.org/whl/cpu
    - name: Install package
      run: |
        python -m pip install -e .[all]
    - name: Make doc
      run: |
        cd doc
        make html
        cd ..
    - name: Push docs on main update
      if: success() && github.ref == 'refs/heads/main'
      run: |
        # Add deploy key and clone through ssh
        eval "$(ssh-agent -s)"
        mkdir ~/.ssh
        echo "${{ secrets.DOC_DEPLOY_PRIVATE_KEY }}" > ~/.ssh/id_rsa
        chmod 600 ~/.ssh/id_rsa
        ssh-keyscan -t rsa github.com
        echo 'Documentation was successfully built, updating the website.'
        # See https://github.community/t/github-actions-bot-email-address/17204/5
        git config --global user.email "41898282+github-actions[bot]@users.noreply.github.com"
        git config --global user.name "github-actions"
        git clone "**************:neuraloperator/neuraloperator.github.io.git" doc_folder
        echo "-- Updating the content"
        cd doc_folder
        git rm -r dev/*
        mkdir -p dev
        echo "Copying to folder"
        cp -r ../doc/build/html/* dev/
        echo "Pushing to git"
        git add dev
        git commit -m "Github action: auto-update."
        git push --force origin main
