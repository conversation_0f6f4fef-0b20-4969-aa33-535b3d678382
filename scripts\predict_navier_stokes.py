import sys
import argparse
import json
from pathlib import Path

import numpy as np
import matplotlib.pyplot as plt
import torch
from configmypy import YamlConfig

from neuralop import get_model
from neuralop.data.transforms.data_processors import MGPatchingDataProcessor

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='Navier-Stokes方程预测脚本')
    parser.add_argument('--model_dir', type=str, required=True,
                        help='保存的模型目录路径，例如: ./saved_models/navier_stokes_YYYYMMDD_HHMMSS')
    parser.add_argument('--input_file', type=str, required=True,
                        help='输入数据文件路径(.npy或.pt格式)')
    parser.add_argument('--output_dir', type=str, default='./predictions',
                        help='预测结果保存目录')
    parser.add_argument('--visualize', action='store_true',
                        help='是否可视化预测结果')
    parser.add_argument('--gpu', type=int, default=0,
                        help='使用的GPU设备ID，-1表示使用CPU')
    return parser.parse_args()

def load_config(config_path):
    """加载配置文件"""
    try:
        # 尝试直接读取YAML文件
        config = YamlConfig(config_file=config_path).read_conf()
        return config
    except:
        # 如果上面的方法失败，尝试另一种方式
        with open(config_path, 'r') as f:
            config_text = f.read()
        
        # 如果配置以字符串形式保存，我们需要解析它
        # 这里是一个简单的办法，实际情况可能需要更复杂的处理
        if config_text.startswith("ConfigurationView"):
            print("配置文件是ConfigurationView格式，尝试手动解析...")
            
            # 创建一个简单的配置对象作为替代
            class SimpleConfig:
                def __init__(self, config_dict):
                    for key, value in config_dict.items():
                        if isinstance(value, dict):
                            setattr(self, key, SimpleConfig(value))
                        else:
                            setattr(self, key, value)
            
            # 尝试从配置文件构建一个配置对象
            # 这是一个简化的例子，实际情况可能需要更复杂的解析
            config_dict = {
                "arch": "fno",
                "fno": {
                    "data_channels": 1,
                    "out_channels": 1,
                    "n_modes": [64, 64],
                    "hidden_channels": 64,
                    "n_layers": 4
                }
            }
            
            # 如果有摘要文件，尝试从摘要文件获取更多信息
            summary_path = Path(config_path).parent / "training_summary.json"
            if summary_path.exists():
                with open(summary_path, 'r') as f:
                    summary = json.load(f)
                    if "fno_modes" in summary:
                        config_dict["fno"]["n_modes"] = summary["fno_modes"]
                    if "fno_layers" in summary:
                        config_dict["fno"]["n_layers"] = summary["fno_layers"]
            
            return SimpleConfig(config_dict)

def load_model(model_dir):
    """加载模型和相关组件"""
    model_dir = Path(model_dir)
    
    # 检查必要文件是否存在
    model_path = model_dir / "model.pth"
    config_path = model_dir / "config.yaml"
    processor_path = model_dir / "data_processor.pth"
    
    if not model_path.exists():
        raise FileNotFoundError(f"模型文件不存在: {model_path}")
    
    # 加载配置
    if config_path.exists():
        config = load_config(config_path)
        print(f"已加载配置")
    else:
        raise FileNotFoundError(f"配置文件不存在: {config_path}")
    
    # 创建模型
    model = get_model(config)
    print(f"已创建模型")
    
    # 加载模型权重
    model.load_state_dict(torch.load(model_path))
    print(f"已加载模型权重")
    
    # 加载数据处理器
    if processor_path.exists():
        try:
            data_processor = torch.load(processor_path)
            print(f"已加载数据处理器")
        except Exception as e:
            print(f"加载数据处理器时出错: {e}")
            print("尝试重新创建数据处理器...")
            # 如果加载失败，尝试重新创建
            if config.patching.levels > 0:
                data_processor = MGPatchingDataProcessor(
                    model=model,
                    padding_fraction=config.patching.padding,
                    stitching=config.patching.stitching,
                    levels=config.patching.levels
                )
            else:
                # 创建一个简单的数据处理器，可能需要调整
                class SimpleDataProcessor:
                    def __init__(self):
                        pass
                    
                    def preprocess(self, x):
                        # 简单的预处理，可能需要调整
                        return x
                    
                    def postprocess(self, x):
                        # 简单的后处理，可能需要调整
                        return x
                    
                    def to(self, device):
                        return self
                
                data_processor = SimpleDataProcessor()
            print("已创建替代数据处理器")
    else:
        raise FileNotFoundError(f"数据处理器文件不存在: {processor_path}")
    
    return model, data_processor, config

def load_input_data(input_file):
    """加载输入数据"""
    input_path = Path(input_file)
    if not input_path.exists():
        raise FileNotFoundError(f"输入文件不存在: {input_path}")
    
    # 根据文件扩展名加载数据
    if input_path.suffix.lower() == '.npy':
        data = np.load(input_path)
        input_tensor = torch.from_numpy(data).float()
    elif input_path.suffix.lower() == '.pt':
        input_tensor = torch.load(input_path)
    else:
        raise ValueError(f"不支持的文件格式: {input_path.suffix}")
    
    # 确保输入数据的形状正确
    if len(input_tensor.shape) == 3:  # [channels, height, width]
        input_tensor = input_tensor.unsqueeze(0)  # 添加批次维度 [1, channels, height, width]
    
    print(f"输入数据形状: {input_tensor.shape}")
    return input_tensor

def visualize_flow_field(flow_field, title="预测的流体场", save_path=None):
    """可视化流体场"""
    # 确保数据是numpy数组
    if isinstance(flow_field, torch.Tensor):
        flow_field = flow_field.cpu().numpy()
    
    # 假设流体场形状是[batch, channels, height, width]
    if len(flow_field.shape) == 4:
        flow_field = flow_field[0]  # 只取第一个批次样本
    
    # 如果有多个通道，分别可视化
    if flow_field.shape[0] > 1:
        u = flow_field[0]  # 速度x分量
        v = flow_field[1] if flow_field.shape[0] > 1 else None
        p = flow_field[2] if flow_field.shape[0] > 2 else None
        
        n_plots = sum(1 for x in [u, v, p] if x is not None)
        fig, axes = plt.subplots(1, n_plots, figsize=(6*n_plots, 5))
        
        if n_plots == 1:
            axes = [axes]
        
        plot_idx = 0
        if u is not None:
            im = axes[plot_idx].imshow(u, cmap='viridis')
            plt.colorbar(im, ax=axes[plot_idx])
            axes[plot_idx].set_title("速度 x-分量")
            plot_idx += 1
        
        if v is not None:
            im = axes[plot_idx].imshow(v, cmap='viridis')
            plt.colorbar(im, ax=axes[plot_idx])
            axes[plot_idx].set_title("速度 y-分量")
            plot_idx += 1
        
        if p is not None:
            im = axes[plot_idx].imshow(p, cmap='viridis')
            plt.colorbar(im, ax=axes[plot_idx])
            axes[plot_idx].set_title("压力")
    else:
        # 单通道场
        plt.figure(figsize=(8, 6))
        im = plt.imshow(flow_field[0], cmap='viridis')
        plt.colorbar(im)
    
    plt.suptitle(title)
    
    if save_path:
        plt.savefig(save_path)
        print(f"已保存可视化结果到: {save_path}")
    
    plt.show()

def predict(model, data_processor, input_tensor, device):
    """使用模型进行预测"""
    # 将模型和输入移动到指定设备
    model = model.to(device)
    model.eval()  # 设置为评估模式
    input_tensor = input_tensor.to(device)
    
    # 预处理输入
    try:
        processed_input = data_processor.preprocess(input_tensor)
    except:
        # 如果预处理失败，直接使用原始输入
        print("预处理失败，使用原始输入...")
        processed_input = input_tensor
    
    # 使用模型进行预测
    with torch.no_grad():
        output = model(processed_input)
    
    # 后处理输出
    try:
        final_output = data_processor.postprocess(output)
    except:
        # 如果后处理失败，直接使用原始输出
        print("后处理失败，使用原始输出...")
        final_output = output
    
    return final_output

def main():
    # 解析命令行参数
    args = parse_args()
    
    # 设置设备
    if args.gpu >= 0 and torch.cuda.is_available():
        device = torch.device(f"cuda:{args.gpu}")
        print(f"使用GPU: {torch.cuda.get_device_name(args.gpu)}")
    else:
        device = torch.device("cpu")
        print("使用CPU")
    
    # 创建输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    try:
        # 加载模型和数据处理器
        model, data_processor, config = load_model(args.model_dir)
        
        # 加载输入数据
        input_tensor = load_input_data(args.input_file)
        
        # 执行预测
        output = predict(model, data_processor, input_tensor, device)
        
        # 保存预测结果
        output_file = output_dir / f"prediction_{Path(args.input_file).stem}.pt"
        torch.save(output, output_file)
        print(f"预测结果已保存到: {output_file}")
        
        # 另存为numpy格式
        output_numpy = output.cpu().numpy()
        np_output_file = output_dir / f"prediction_{Path(args.input_file).stem}.npy"
        np.save(np_output_file, output_numpy)
        print(f"预测结果（NumPy格式）已保存到: {np_output_file}")
        
        # 可视化结果
        if args.visualize:
            vis_path = output_dir / f"visualization_{Path(args.input_file).stem}.png"
            visualize_flow_field(output, save_path=vis_path)
        
        print("预测完成！")
        
    except Exception as e:
        print(f"预测过程中出错: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main() 